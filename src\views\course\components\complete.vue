<script setup>
import { onBeforeMount, ref, onMounted, nextTick, defineEmits } from "vue";
import Scheduling from "./scheduling.vue";
import PriceSetting from "./priceSetting.vue";
import courseIntroduction from "./courseIntroduction.vue";
import JobDesign from "./jobDesign.vue";
import Foundation from "./foundation.vue";
import OrderDialog from "@/components/Base/orderDialog.vue";

import { to } from "@iceywu/utils";
import { requestTo } from "@/utils/http/tool";
import { useRoute, useRouter } from "vue-router";
import { ElMessage, ElMessageBox } from "element-plus";
import KnowledgeContent from "@/views/course/components/knowledge.vue";
import {
  draftCourseIntroductionFindByDraftId,
  draftCourseKnowledgePointFindByDraftId,
  draftEquipmentDescriptionFindByDraftId,
  draftPrecautionsFindByDraftId,
  draftUserAgreementFindByDraftId,
  coursePeriodSaveV2,
  draftCoursePeriodFindByDraftId,
  draftDelete
} from "@/api/drafts.js";
import { getAsyncTask } from "@/utils/common";
import { periodOpenGroupOrder } from "@/api/course.js";
import { courseStore } from "@/store/modules/course.js";
const props = defineProps({
  draftId: {
    type: String,
    default: ""
  },
  couponData: {
    type: Object,
    default: () => ({
      couponAvailable: false,
      couponIds: [],
      selectedCouponsData: []
    })
  }
});

const emits = defineEmits(["completeBelowEvt"]);

const useCourseStore = courseStore();

const route = useRoute();
const router = useRouter();
const draftId = ref(""); // 草稿 课期id
onBeforeMount(() => {
  draftId.value = Number(route.query.draftId) || Number(useCourseStore.draftId);
});

onMounted(() => {
  nextTick();
  boxObserve();
  PeriodCourseAdd();
});

const tabsBox = [
  { id: 1, label: "课程基础信息" },
  { id: 2, label: "课期基础信息" },
  { id: 3, label: "课期行程" },
  { id: 4, label: "课期介绍" },
  { id: 5, label: "课期知识点" },
  { id: 6, label: "实践感悟" },
  { id: 7, label: "材料说明" },
  { id: 8, label: "注意事项" },
  { id: 9, label: "用户协议" },
  { id: 10, label: "价格设置" }
];
const btnData = ref([
  { id: 1, name: "放弃新建" },
  { id: 2, name: "保存到草稿箱" }
]);

const getApiTypeApi = val => {
  let res = "";
  switch (val) {
    case "课期介绍":
      getIntroductionfindById(val);
      break;
    case "课期知识点":
      getIntroductionfindById(val);
      break;
    case "材料说明":
      getIntroductionfindById(val);
      break;
    case "注意事项":
      getIntroductionfindById(val);
      break;
    case "用户协议":
      getIntroductionfindById(val);
      break;
    default:
      break;
  }
  return res;
};

const observer = new IntersectionObserver(
  (entries, observer) => {
    entries.forEach((entry, index) => {
      if (entry.isIntersecting) {
        const loadElement = entry.target;
        getApiTypeApi(loadElement.textContent);
        observer.unobserve(entry.target); // 加载后停止观察
      }
    });
  },
  {
    // 可选配置
    root: null, // 默认视口，也可指定父容器
    rootMargin: "0px", // 扩展/缩小检测区域（如提前加载）
    threshold: 0.5 // 触发回调的可见比例阈值（0~1）
  }
);

// ref 数组
const itemRefs = ref([]);
// 设置 ref
const setItemRef = (el, index) => {
  if (el) {
    itemRefs.value[index] = el;
  }
};
function boxObserve() {
  const boxElements = document.querySelectorAll(".title-box");
  boxElements.forEach(el => observer.observe(el));
}

const getApiType = val => {
  let res = "";
  switch (val) {
    case "课期介绍":
      res = draftCourseIntroductionFindByDraftId;
      break;
    case "课期知识点":
      res = draftCourseKnowledgePointFindByDraftId;
      break;
    case "材料说明":
      res = draftEquipmentDescriptionFindByDraftId;
      break;
    case "注意事项":
      res = draftPrecautionsFindByDraftId;
      break;
    case "用户协议":
      res = draftUserAgreementFindByDraftId;
      break;
    default:
      break;
  }
  return res;
};

function valueHtml(val) {
  const text = ref("");
  if (!val) return text.value;
  if (val === "<p><br></p>") {
    return (text.value = "");
  } else {
    return (text.value = val);
  }
}

const coursePeriod = ref("");
const knowledge = ref("");
const equipment = ref("");
const payAttention = ref("");
const agreement = ref("");
// 查询详情
const getIntroductionfindById = async typeName => {
  const params = {
    draftId: Number(route.query.draftId) || Number(useCourseStore.draftId)
  };
  let api = getApiType(typeName, false);
  let [err, res] = await requestTo(api(params));
  if (res) {
    if (typeName === "课期介绍") {
      coursePeriod.value = valueHtml(res.content);
    } else if (typeName === "课期知识点") {
      knowledge.value = valueHtml(res.content);
    } else if (typeName === "材料说明") {
      equipment.value = valueHtml(res.content);
    } else if (typeName === "注意事项") {
      payAttention.value = valueHtml(res.content);
    } else if (typeName === "用户协议") {
      agreement.value = valueHtml(res.content);
    }
  } else {
    // console.log("🐬-----err-----", err);
  }
};

const upperBelowEvt = te => {
  let obj = {
    type: te,
    draftId: Number(route.query.draftId) || Number(useCourseStore.draftId),
    infoShow: "价格设置"
  };
  emits("completeBelowEvt", obj);
};

const buttData = ref([
  { id: 1, name: "仅新建" },
  { id: 2, name: "新建并申请上架" },
  { id: 3, name: "新建并定制课程" }
]);

const dropdownButt = val => {
  if (val.id === 3) {
    dialogFormVisible.value = true;
  } else {
    ElMessageBox.confirm(`确定要${val.name}该课程吗`, "完成新建提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消"
    })
      .then(async () => {
        if (val.id === 1) {
          const isMsg = {
            isOnline: false,
            isOpenGroup: false
          };
          coursePeriodSaveEvt(isMsg, val);
        } else if (val.id === 2) {
          const isMsg = {
            isOnline: true,
            isOpenGroup: false
          };
          coursePeriodSaveEvt(isMsg, val);
        }
      })
      .catch(() => {});
  }
};

const k = ref(false);
const coursePeriodSaveEvt = async (val, text) => {
  console.log("🦄-----val, text-----", val, text);
  console.log("🎁-----优惠券数据-----", props.couponData);
  console.log("🎁-----优惠券可用状态-----", props.couponData?.couponAvailable);
  console.log("🎁-----优惠券ID数组-----", props.couponData?.couponIds);

  const operateLog = {
    operateLogType: "COURSE_MANAGEMENT",
    operateType: `${text.name}了“${nameTitle.value}”课期`
    // operatorTarget: form.value.name,
  };
  const params = {
    draftId: Number(route.query.draftId) || Number(useCourseStore.draftId),
    isOnline: val.isOnline,
    isOpenGroup: val.isOpenGroup
  };

  // 添加优惠券数据
  if (props.couponData && props.couponData.couponAvailable) {
    // 对优惠券ID数组进行去重处理
    params.couponIds = [...new Set(props.couponData.couponIds || [])];
    console.log("🎁-----添加优惠券ID到参数(已去重)-----", params.couponIds);
  } else {
    console.log("🎁-----优惠券不可用或数据为空，不添加couponIds-----");
  }

  console.log("🎁-----最终请求参数-----", params);
  let { code, data } = await coursePeriodSaveV2(params, operateLog);
  // console.log("🍭-----code, data-----", code, data);
  if (code == 200 && data?.id) {
    const task = await getAsyncTask(data?.id);
    // console.log("🐬-----code, data-----", task.code, task.data);
    if (task.data.complete && task.data.success) {
      let resData = {};
      if (task.data.res) {
        resData = JSON.parse(task.data.res);
        // console.log("🌵-----resData-----", resData);
        if (k.value) {
          if (text.id === 1) {
            router.push({
              path: "/course/courseDetails/currentDetails",
              query: {
                courseId: resData.courseId,
                periodId: resData.coursePeriodId
              }
            });
          } else if (text.id === 2) {
            router.push({
              path: "/course/courseDetails",
              query: { id: resData.courseId }
            });
          } else if (text.id === 3) {
            router.replace({
              path: "/course/currentDetails/groupOrder",
              query: { periodId: resData.coursePeriodId }
            });
          }
        } else {
          router.push({
            path: "/course/courseDetails",
            query: { id: resData.courseId }
          });
        }
      }
      dialogFormVisible.value = false;
      getListLoading.value = false;
      ElMessage.success(text.name + "成功");
    } else {
      dialogFormVisible.value = false;
      getListLoading.value = false;
      ElMessage.error(task.data.errMsg);
    }
  }
};

// 团购弹窗
const dialogFormVisible = ref(false);
// 团购分享
const btnOKClick = id => {
  dialogFormVisible.value = true;
  getListLoading.value = true;
  const isMsg = {
    isOnline: false,
    isOpenGroup: true
  };
  const val = buttData.value[2];
  coursePeriodSaveEvt(isMsg, val);
};

const getListLoading = ref(false);
const cancel = () => {
  dialogFormVisible.value = false;
  getListLoading.value = false;
};

const nameTitle = ref("");
// 课期基础信息
const PeriodCourseAdd = async () => {
  let [err, res] = await to(
    draftCoursePeriodFindByDraftId({ draftId: draftId.value })
  );
  if (res) {
    const { data } = res;
    nameTitle.value = data?.name || "";
    if (data?.draftCoursePeriodTimes.length > 1) {
      k.value = false;
    } else {
      k.value = true;
    }
  } else {
  }
};
const deleteDraft = async () => {
  let operateLogDraft = {
    operateLogType: "COURSE_MANAGEMENT",
    operateType: `删除了草稿id为“${draftId.value}”的草稿数据`
  };
  const [err, res] = await to(
    draftDelete({ id: draftId.value }, operateLogDraft)
  );
  if (res.code === 200) {
    ElMessage.success("删除成功");
  } else {
    ElMessage.error("删除失败");
  }
};

// 退出
const backEvt = (val, it) => {
  console.log("🍭-----val, it-----", val, it);
  if (val === "exit") {
    let titlt =
      it.id === 1
        ? `请确认是否清除当前编辑和提交的所有资料，并删除当前编辑资料在草稿箱中对应的草稿条目`
        : `请确认是否将当前编辑和提交的所有资料保存到草稿箱，并退出编辑页面`;
    ElMessageBox.confirm(`${titlt}`, `退出并${it.name}`, {
      confirmButtonText: "确定",
      cancelButtonText: "取消"
    }).then(() => {
      if (it.id === 1) {
        deleteDraft();
        if (route.query.type === "draft") {
          router.replace("/course/drafts");
        } else if (route.query.type === "edite") {
          router.replace("/course/courseDetails");
        } else if (route.query.type === "createPeriodDetail") {
          router.replace({
            path: "/course/courseDetails",
            query: { id: route.query.courseId }
          });
        } else if (
          route.query.type === "create" ||
          route.query.type === "createPeriod"
        ) {
          router.replace("/course/courseManage/index");
        } else {
          router.go(-1);
        }
      } else if (it.id === 2) {
        if (route.query.type === "draft") {
          router.replace("/course/drafts");
        } else if (route.query.type === "edite") {
          router.replace("/course/courseDetails");
        } else if (route.query.type === "createPeriodDetail") {
          router.replace({
            path: "/course/courseDetails",
            query: { id: route.query.courseId }
          });
        } else if (
          route.query.type === "create" ||
          route.query.type === "createPeriod"
        ) {
          router.replace("/course/courseManage/index");
        } else {
          router.go(-1);
        }
      }
    });
  } else {
    if (route.query.fromPage === "courseDetail") {
      router.replace({
        path: "/course/courseDetails",
        query: { id: route.query.courseId }
      });
    } else if (route.query.fromPage === "currentDetail") {
      router.replace({
        path: "/course/courseDetails/currentDetails",
        query: {
          periodId: route.query.periodId,
          courseId: route.query.courseId
        }
      });
    }
  }
};
</script>

<template>
  <div class="conment">
    <el-scrollbar class="scrollbar" height="calc(100vh - 310px)">
      <div
        v-for="(item, index) in tabsBox"
        :key="item.id"
        :ref="el => setItemRef(el, index)"
        class="box"
      >
        <el-divider content-position="left" class="title-box">
          {{ item.label }}
        </el-divider>

        <Foundation
          v-if="item.label === '课程基础信息'"
          title="课程基础信息"
          :draftId="draftId"
        />
        <Foundation
          v-else-if="item.label === '课期基础信息'"
          title="课期基础信息"
          :draftId="draftId"
        />
        <Scheduling v-else-if="item.label === '课期行程'" :draftId="draftId" />

        <courseIntroduction
          v-else-if="item.label === '课期介绍'"
          :showContent="coursePeriod"
        />
        <!-- <courseIntroduction
          v-else-if="item.label === '课期知识点'"
          :showContent="knowledge"
        /> -->
        <KnowledgeContent
          v-else-if="item.label === '课期知识点'"
          :draftId="draftId"
        />
        <courseIntroduction
          v-else-if="item.label === '材料说明'"
          :showContent="equipment"
        />
        <courseIntroduction
          v-else-if="item.label === '注意事项'"
          :showContent="payAttention"
        />
        <courseIntroduction
          v-else-if="item.label === '用户协议'"
          :showContent="agreement"
        />

        <JobDesign v-else-if="item.label === '实践感悟'" :draftId="draftId" />
        <PriceSetting
          v-else-if="item.label === '价格设置'"
          :draftId="draftId"
          :coursePeriodId="18"
          :height="'94%'"
          :width="'calc(100vw - 478px)'"
          :useExternalCouponData="true"
          :externalCouponData="couponData.selectedCouponsData || []"
        />
      </div>
    </el-scrollbar>
    <div class="butt">
      <div>
        <!-- <el-button @click="router.go(-1)">退出</el-button> -->
        <!-- <el-button type="primary">保存草稿箱</el-button> -->
        <el-dropdown>
          <el-button style="margin-right: 10px">退出</el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item
                v-for="it in btnData"
                :key="it.id"
                @click="backEvt('exit', it)"
              >
                {{ it.name }}
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
        <el-button type="primary" @click="upperBelowEvt('upper')">
          上一步
        </el-button>
      </div>
      <el-dropdown>
        <el-button type="primary">完成新建</el-button>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item
              v-for="it in buttData"
              :key="it.id"
              @click="dropdownButt(it)"
            >
              {{ it.name }}
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>

    <el-dialog v-model="dialogFormVisible" title="开启课程定制确认" width="500">
      <div class="content">
        <div class="describe">
          <p>说明:</p>
          <p>1、一旦开启课程定制，该课程将无法在平台上进行公开展示，只</p>
          <p>能通过分享链接的方式让特定人群进行购买。</p>
          <p>2、一旦开启课程定制，该课程的所有信息将无法被修改。如需修</p>
          <p>改请取消定制后再进行修改。</p>
          <p>3、如果一旦有用户付款下单且没有退货，则无法取消定制。</p>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button :style="{ 'margin-right': '10px' }" @click="cancel">
            取消
          </el-button>
          <el-button
            :loading="getListLoading"
            :type="'primary'"
            @click="btnOKClick"
          >
            确认开启
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
.conment {
  box-sizing: border-box;
  width: 100%;
  // padding: 0 10px 0 0;
  .scrollbar {
    margin-bottom: 20px;
  }

  .box {
    height: 100%;
    width: 100%;
    // background-color: rgb(199, 197, 197);
    // margin-bottom: 20px;

    // .title-box {
    //   padding-top: 20px;
    //   width: 100%;
    //   height: 20px;
    //   line-height: 20px;
    //   font-size: 20px;
    //   font-weight: 900;
    //   color: rgba(16, 16, 16, 1);
    //   text-align: left;
    // }
  }

  .butt {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}

.describe {
  width: 80%;
  height: 130px;
  // border:1px solid red;
  margin: 0 auto;
  font-size: 14px;
}
// :deep(.el-button:focus-visible) {
//   display: none;
// }
</style>
