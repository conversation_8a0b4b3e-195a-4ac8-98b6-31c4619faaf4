<script setup>
import {
  onMounted,
  ref,
  reactive,
  defineEmits,
  onBeforeMount,
  onUnmounted
} from "vue";
import { useRouter, useRoute } from "vue-router";
import { Edit, Delete, Loading } from "@element-plus/icons-vue";
import { ElMessage, ElMessageBox, ElLoading } from "element-plus";
import RichEditor from "@/components/Base/RichEditor.vue";
import { findByCoursePeriodId, itineraryUpdateV2 } from "@/api/period.js";
import {
  coursePeriodFind,
  complexId,
  leaderLecturerFind
} from "@/api/course.js";
import { requestTo } from "@/utils/http/tool";
import {
  ItineraryFindByDraftId,
  ItineraryNextDraftItinerary,
  itinerarySaveDraftItinerary,
  draftCoursePeriodFindByDraftId,
  draftDelete
} from "@/api/drafts.js";
import dayjs from "dayjs";
import { removeEmptyValues, to, deepClone, isEmpty } from "@iceywu/utils";
import { aiNewPage } from "@/utils/aiTool.js";
import { getAsyncTask, vETgetAsyncTask } from "@/utils/common";
import { questionAsk } from "@/api/aiQuestion.js";
import { courseStore } from "@/store/modules/course.js";
import { tripValue } from "@/utils/defaultValue.js";
import { useTripStore } from "@/store/modules/trip.js";
import { whitePath } from "@/utils/common.js";

const props = defineProps({
  // draftId: {
  //   type: String,
  //   default: ""
  // },
  isNewEdit: {
    type: String,
    default: "new"
  },
  periodName: {
    type: String,
    default: "未命名"
  },
  infoShowEnName: {
    type: String,
    default: "foundation"
  }
});

const emits = defineEmits(["upperBelowEvt"]);
const useCourseStore = courseStore();
const tripStore = useTripStore();
let handleCancel = null;
// console.log("🦄-----useCourseStore-----", useCourseStore?.draftId);

const router = useRouter();
const route = useRoute();
const ruleFormRef = ref();

// 表单引用
const formRefs = ref([]);

// 实践点选项
const placeOptions = ref([
  // { label: "历史博物馆", value: "museum" },
  // { label: "科技展览馆", value: "tech-expo" },
  // { label: "自然保护区", value: "nature-reserve" }
]);

// 讲师选项
const teacherOptions = ref([
  // { label: "张教授", value: "zhang" },
  // { label: "李老师", value: "li" },
  // { label: "王博士", value: "wang" }
]);
const openTime = ref("");
// 日期禁用逻辑：禁止选择小于开课时间的日期
const disabledDate = time => {
  if (!openTime.value) return false;
  if (dialogTitle.value === "新增") {
    return time.getTime() < dayjs(openTime.value).startOf("day").valueOf();
  } else {
    return time.getTime() < dayjs(openTime.value).startOf("day").valueOf();
  }
};
const validateDate = (rule, value, callback) => {
  if (!value) {
    callback(new Error("请选择日期"));
  } else {
    callback();
  }
};
const validateTime = (rule, value, callback) => {
  if (!value) {
    callback(new Error("请选择时间"));
  } else if (
    dayjs(
      dayjs(form.value.date).format("YYYY-MM-DD") +
      "" +
      dayjs(value).format("HH:mm:ss")
    ).valueOf() < dayjs(openTime.value).valueOf()
  ) {
    if (dialogTitle.value === "新增") {
      if (tripList.value.at(-1)) {
        callback(new Error(`时间不能早于${tripList.value.at(-1).title}时间`));
      } else {
        callback(new Error("时间不能早于开课时间"));
      }
    } else {
      callback(new Error("时间不能早于开课时间"));
    }
  } else {
    callback();
  }
};
const getRules = index => {
  return {
    // 天数验证规则
    gapDays: [
      { required: true, message: "请输入第几天", trigger: "change" },
      {
        type: "number",
        min: 1,
        message: "天数必须大于0",
        trigger: "change"
      },
      {
        validator: (rule, value, callback) => {
          // 检查天数顺序是否正确
          if (value && tripList.value.length > 1) {
            const currentDay = Number(value);

            // 检查前一个行程点（如果存在）
            if (index > 0) {
              const prevItem = tripList.value[index - 1];
              if (prevItem.gapDays && Number(prevItem.gapDays) > currentDay) {
                callback(new Error("不能小于前一个行程的天数"));
                return;
              }
            }

            // 检查后一个行程点（如果存在）
            if (index < tripList.value.length - 1) {
              const nextItem = tripList.value[index + 1];
              if (nextItem.gapDays && currentDay > Number(nextItem.gapDays)) {
                callback(new Error("不能大于后一个行程的天数"));
                return;
              }
            }
          }
          callback();
        },
        trigger: "change"
      }
    ],
    // 时间验证规则
    time: [
      { required: true, message: "请选择时间", trigger: "change" },
      {
        validator: (rule, value, callback) => {
          // 检查时间是否早于开课时间
          if (value) {
            // 获取当前行程项的天数
            const currentGapDays = tripList.value[index]?.gapDays || 1;

            // 计算应该比较的时间：开课日期 + 天数偏移 + 当前选择的时间
            const checkDateTime =
              dayjs(openTimeNian.value)
                .add(currentGapDays - 1, "day") // gapDays是从1开始的，所以减1
                .format("YYYY-MM-DD") +
                " " +
                value;

            const itemDateTime = dayjs(checkDateTime).valueOf();

            if (itemDateTime < openTimeValue.value) {
              callback(new Error("时间不能早于开课时间"));
              return;
            }
          }
          callback();
        },
        trigger: "change"
      },
      {
        validator: (rule, value, callback) => {
          // 检查是否与同一天的其他行程时间冲突
          const currentDay = tripList.value[index].gapDays;
          let hasConflict = false;

          tripList.value.forEach((item, i) => {
            if (
              i !== index &&
              item.gapDays === currentDay &&
              item.time === value
            ) {
              hasConflict = true;
            }
          });

          if (hasConflict) {
            callback(new Error("同一天内不能有相同时间的行程安排"));
          } else {
            callback();
          }
        },
        trigger: "change"
      },
      {
        validator: (rule, value, callback) => {
          // 检查时间顺序是否正确（上一个时间点不能大于下一个时间点）
          if (value && tripList.value.length > 1) {
            const currentItem = tripList.value[index];
            const currentDay = currentItem.gapDays;
            const currentTime = dayjs(value, "HH:mm:ss");

            // 检查前面的行程点
            for (let i = 0; i < index; i++) {
              const prevItem = tripList.value[i];
              // 如果前面的行程点在同一天或更晚的天数
              if (prevItem.gapDays >= currentDay && prevItem.time) {
                const prevTime = dayjs(prevItem.time, "HH:mm:ss");
                // 如果是同一天且当前时间早于前面的时间，则报错
                if (
                  prevItem.gapDays === currentDay &&
                  currentTime.isBefore(prevTime)
                ) {
                  callback(new Error("同一行程日的时间不能早于前一个行程点"));
                  return;
                }
              }
            }

            // 检查后面的行程点
            for (let i = index + 1; i < tripList.value.length; i++) {
              const nextItem = tripList.value[i];
              // 如果后面的行程点在同一天或更早的天数
              if (nextItem.gapDays <= currentDay && nextItem.time) {
                const nextTime = dayjs(nextItem.time, "HH:mm:ss");
                // 如果是同一天且当前时间晚于后面的时间，则报错
                if (
                  nextItem.gapDays === currentDay &&
                  currentTime.isAfter(nextTime)
                ) {
                  callback(new Error("同一行程日的时间不能晚于后一个行程点"));
                  return;
                }
              }
            }
          }
          callback();
        },
        trigger: "change"
      }
    ],
    // 实践点验证规则
    complexId: [{ required: true, message: "请选择实践点", trigger: "blur" }],
    // 讲师验证规则
    lecturerId: [{ required: true, message: "请选择讲师", trigger: "blur" }],
    // 主题验证规则
    title: [
      { required: true, message: "请编辑行程主题", trigger: "change" },
      { min: 1, max: 50, message: "长度在 1 到 50 个字符", trigger: "change" }
    ],
    // 内容验证规则
    content: [
      { required: true, message: "请编辑行程内容", trigger: "change" },
      {
        validator: (rule, value, callback) => {
          if (!value || value.trim() === "" || value === "<p><br></p>") {
            callback(new Error("行程内容不能为空"));
          } else {
            callback();
          }
        },
        trigger: "change"
      }
    ]
  };
};

const rules = reactive({
  title: [{ required: true, message: "请输入行程标题", trigger: "blur" }],
  date: [{ required: true, validator: validateDate, trigger: "blur" }],
  title: [{ required: true, message: "请输入主题", trigger: "change" }],
  content: [{ required: true, message: "请输入内容", trigger: "change" }]
});

const tripList = ref([
  {
    gapDays: null,
    time: "",
    complexId: "",
    lecturerId: "",
    title: "",
    content: ""
  }
]);
const textarea2 = ref("");
const valueHtml = ref("");
const form = ref({
  // 弹框表单
  title: "",
  gapDays: "",
  time: "",
  timeA: "",
  content: "",
  index: "",
  complexId: "",
  lecturerId: ""
});
const dialogFormVisible = ref(false);
const submitLoading = ref(false);
const dialogTitle = ref(""); // 弹框标题
const btnData = ref([
  { id: 1, name: "放弃新建" },
  { id: 2, name: "保存到草稿箱" }
]);

// 弹出框-打开
const isDialog = (text, val, a) => {
  // console.log("🐬-----text, val, a-----", text, val, a);
  if (ruleFormRef.value) ruleFormRef.value.resetFields();
  dialogFormVisible.value = true;
  if (text === "newly") {
    dialogTitle.value = "新增";
    form.value = {};
    valueHtml.value = "";
  } else {
    openTime.value = tripList.value[0].time;

    dialogTitle.value = "编辑";
    form.value.index = a || "";
    form.value.title = val.title;
    form.value.gapDays = val.gapDays;
    form.value.time = val.time;
    form.value.timeA = val.timeA;
    valueHtml.value = val.content;
    form.value.complexId = val.complexId;
    form.value.lecturerId = val.lecturerId;
  }
};
// 弹出框-新增及编辑行程点
const addItineraryCreate = async formEl => {
  if (!formEl) return;
  if (submitLoading.value) return;
  submitLoading.value = true;
  await formEl.validate(async (valid, fields) => {
    if (valid) {
      if (
        !valueHtml.value ||
        valueHtml.value.trim() === "" ||
        valueHtml.value === "<p><br></p>"
      ) {
        ElMessage.error("请填写行程点内容");
        return;
      }
      const p = {
        content: valueHtml.value,
        title: form.value.title,
        gapDays: form.value.gapDays || "",
        time: form.value.time || "",
        timeA: form.value.timeA || "",
        complexId: form.value.complexId || "",
        lecturerId: form.value.lecturerId || ""
      };
      // console.log("🐬-----p--新增及编辑信息---", p);
      if (dialogTitle.value === "编辑") {
        tripList.value.splice(form.value.index, 1, p);
        submitLoading.value = false;
        dialogFormVisible.value = false;
      } else {
        tripList.value.push(p);

        submitLoading.value = false;
        dialogFormVisible.value = false;
      }
    } else {
      console.log("校验不通过!", fields);
    }
  });
  submitLoading.value = false;
};
// 弹出框-取消
const cancel = () => {
  // if (ruleFormRef.value) ruleFormRef.value.resetFields();
  dialogFormVisible.value = false;
  submitLoading.value = false;
  dialogTitle.value = "新增";
  form.value = {};
  valueHtml.value = "";
};
// 删除
const deteleEvt = (val, index) => {
  ElMessageBox.confirm(`确定要删除该条行程吗`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消"
  })
    .then(async () => {
      tripList.value.splice(index, 1);
    })
    .catch(() => {});
};
const getCurrentDateTimestamp = val => {
  const today = new Date();
  if (val === 1) {
    today.setHours(9, 30, 0, 0);
    return today.getTime();
  } else if (val === 2) {
    today.setHours(10, 0, 0, 0);
    return today.getTime();
  } else {
    today.setHours(14, 30, 0, 0);
    return today.getTime();
  }
};
// 草稿Id查询行程API
const draftIdTripApi = async () => {
  editCopyClone.value = [];
  const params = {
    draftId: Number(route.query.draftId) || Number(useCourseStore.draftId)
  };
  let [err, res] = await requestTo(ItineraryFindByDraftId(params));
  if (res) {
    if (res.length) {
      // console.log("🐠res-------------222----------------->", res);
      tripList.value = res.map(it => {
        return {
          content: it.content,
          gapDays: it.gapDays,
          id: it.id,
          timeA: timest(it.time),
          time: it.time,
          title: it.title,
          lecturerId: it.lecturer?.id || "",
          complexId: it.complex?.id || ""
        };
      });
      editCopyClone.value = deepClone(tripList.value);
    } else {
      tripList.value = [
        {
          title: "集合出发",
          gapDays: 1,
          // time: "07:30:00",
          // timeA: getCurrentDateTimestamp(1),
          time: "",
          timeA: "",
          content: tripValue.tripOne
        },
        {
          title: "红色征程启航",
          gapDays: 1,
          // time: "08:00:00",
          // timeA: getCurrentDateTimestamp(2),
          time: "",
          timeA: "",
          content: tripValue.tripTwo
        },
        {
          title: "历史回响课堂",
          gapDays: 1,
          // time: "09:30:00",
          // timeA: getCurrentDateTimestamp(3),
          time: "",
          timeA: "",
          content: tripValue.tripThree
        }
      ];
    }
  }
};

const openTimeNian = ref("");
const openTimeValue = ref("");
const CoursePeriodId = ref({});
// 草稿Id查询课期API
const CoursePeriodIdTripApi = async () => {
  const params = {
    draftId: Number(route.query.draftId) || Number(useCourseStore.draftId)
  };
  let [err, res] = await requestTo(draftCoursePeriodFindByDraftId(params));
  if (res) {
    CoursePeriodId.value = res || {};
    let doopTime = CoursePeriodId.value?.draftCoursePeriodTimes || [];
    const maxOpenTime =
      doopTime.length > 0
        ? Math.max(...doopTime.map(item => item.openTime))
        : "";
    openTimeValue.value = maxOpenTime;
    openTimeNian.value = formatTimestamp(maxOpenTime);
  }
};

function deepEqualArrays(arr1, arr2) {
  if (arr1.length !== arr2.length) return false;
  for (let i = 0; i < arr1.length; i++) {
    const obj1 = arr1[i];
    const obj2 = arr2[i];
    const keys1 = Object.keys(obj1);
    const keys2 = Object.keys(obj2);
    if (keys1.length !== keys2.length) return false;
    for (const key of keys1) {
      if (obj1[key] !== obj2[key]) {
        return false;
      }
    }
  }
  return true;
}
const editCopyClone = ref([]);

// 草稿箱 上、下一步+保存草稿箱 按钮
const buttonLoading = ref(false);
const upperBelowEvt = butt => {
  buttonLoading.value = true;
  let obj = {
    type: butt,
    draftId: Number(route.query.draftId) || Number(useCourseStore.draftId),
    infoShow: "基础信息"
  };
  if (butt === "upper") {
    emits("upperBelowEvt", obj);
    buttonLoading.value = false;
    return;
  }
  if (butt === "below") {
    nextStepApi(true);
  } else if (butt === "save") {
    saveApi();
  }
};

// 草稿行程下一步API
const nextStepApi = async val => {
  if (Array.isArray(tripList.value) && tripList.value.length === 0) {
    buttonLoading.value = false;
    return ElMessage.error({
      type: "error",
      message: "请新增行程点"
    });
  }
  // 首先进行表单校验
  let isValid = true;
  let firstErrorIndex = -1;
  let firstErrorMessage = "";

  // 遍历所有表单进行校验
  for (let i = 0; i < tripList.value.length; i++) {
    try {
      // 确保表单引用存在
      if (formRefs.value[i]) {
        await formRefs.value[i].validate();
      } else {
        // 如果表单引用不存在，手动检查字段
        const item = tripList.value[i];
        if (!item.gapDays || item.gapDays <= 0) {
          isValid = false;
          firstErrorIndex = i;
          firstErrorMessage = `第${i + 1}个行程点：请输入正确的天数`;
          break;
        }
        if (!item.time) {
          isValid = false;
          firstErrorIndex = i;
          firstErrorMessage = `第${i + 1}个行程点：请选择时间`;
          break;
        }
        if (!item.complexId) {
          isValid = false;
          firstErrorIndex = i;
          firstErrorMessage = `第${i + 1}个行程点：请选择实践点`;
          break;
        }
        if (!item.lecturerId) {
          isValid = false;
          firstErrorIndex = i;
          firstErrorMessage = `第${i + 1}个行程点：请选择讲师`;
          break;
        }
        if (!item.title || item.title.trim() === "") {
          isValid = false;
          firstErrorIndex = i;
          firstErrorMessage = `第${i + 1}个行程点：请输入行程主题`;
          break;
        }
        if (
          !item.content ||
          item.content.trim() === "" ||
          item.content === "<p><br></p>"
        ) {
          isValid = false;
          firstErrorIndex = i;
          firstErrorMessage = `第${i + 1}个行程点：请输入行程内容`;
          break;
        }
      }
    } catch (error) {
      isValid = false;
      if (firstErrorIndex === -1) {
        firstErrorIndex = i;
        // 提取具体的错误信息
        if (error && typeof error === "object") {
          const errorFields = Object.keys(error);
          if (errorFields.length > 0) {
            const firstField = errorFields[0];
            if (error[firstField] && error[firstField][0]) {
              firstErrorMessage = `第${i + 1}个行程点：${error[firstField][0].message}`;
            }
          }
        }
      }
      break;
    }
  }

  if (!isValid) {
    buttonLoading.value = false;
    ElMessage.error(
      firstErrorMessage || `第${firstErrorIndex + 1}个行程信息填写有误，请检查`
    );
    return;
  }

  const type = deepEqualArrays(tripList.value, editCopyClone.value);
  if (type === true) {
    if (val !== false) {
      let obj = {
        type: "below",
        draftId: Number(route.query.draftId) || Number(useCourseStore.draftId),
        infoShow: "课期介绍",
        complete: true
      };
      emits("upperBelowEvt", obj);
    }
    buttonLoading.value = false;
  } else {
    if (Array.isArray(tripList.value) && tripList.value.length === 0) {
      buttonLoading.value = false;
      return ElMessage.error({
        type: "error",
        message: "请新增行程点"
      });
    }

    const tripData = tripList.value.map(it => {
      return {
        content: it.content,
        gapDays: it.gapDays,
        time: it.time,
        title: it.title,
        lecturerId: it.lecturerId,
        complexId: it.complexId
      };
    });

    const params = {
      draftId: Number(route.query.draftId) || Number(useCourseStore.draftId),
      draftItineraries: tripData
    };
    const operateLog = {
      operateLogType: "COURSE_MANAGEMENT",
      operateType: `将“${props.periodName}”课期中的课期行程保存在草稿箱`
    };
    const newList = removeEmptyValues(params);
    let [err, res] = await to(ItineraryNextDraftItinerary(newList, operateLog));
    if (res) {
      if (res.code === 30040) {
        ElMessage.error(`${res.msg},保存失败。`);
        buttonLoading.value = false;
      } else if (res.code === 200) {
        ElMessage.success("当前资料已保存到草稿箱");
        tripStore.saveBasePlace({});
        tripStore.saveLecturerData({});
        saveCurrentFormToStore();
        buttonLoading.value = false;
        if (val !== false) {
          let obj = {
            type: "below",
            draftId:
              Number(route.query.draftId) || Number(useCourseStore.draftId),
            infoShow: "课期介绍",
            complete: true
          };
          emits("upperBelowEvt", obj);
        }
      }
    } else {
      buttonLoading.value = false;
    }
  }
};
// 草稿行程保存API
const saveApi = async () => {
  if (Array.isArray(tripList.value) && tripList.value.length === 0) {
    buttonLoading.value = false;
    return ElMessage.error({
      type: "error",
      message: "请新增行程点"
    });
  }
  let isValid = true;
  let firstErrorIndex = -1;
  let firstErrorMessage = "";

  // 遍历所有表单进行校验
  for (let i = 0; i < tripList.value.length; i++) {
    try {
      // 确保表单引用存在
      if (formRefs.value[i]) {
        await formRefs.value[i].validate();
      } else {
        // 如果表单引用不存在，手动检查字段
        const item = tripList.value[i];
        if (!item.gapDays || item.gapDays <= 0) {
          isValid = false;
          firstErrorIndex = i;
          firstErrorMessage = `第${i + 1}个行程点：请输入正确的天数`;
          break;
        }
        if (!item.time) {
          isValid = false;
          firstErrorIndex = i;
          firstErrorMessage = `第${i + 1}个行程点：请选择时间`;
          break;
        }
        if (!item.complexId) {
          isValid = false;
          firstErrorIndex = i;
          firstErrorMessage = `第${i + 1}个行程点：请选择实践点`;
          break;
        }
        if (!item.lecturerId) {
          isValid = false;
          firstErrorIndex = i;
          firstErrorMessage = `第${i + 1}个行程点：请选择讲师`;
          break;
        }
        if (!item.title || item.title.trim() === "") {
          isValid = false;
          firstErrorIndex = i;
          firstErrorMessage = `第${i + 1}个行程点：请输入行程主题`;
          break;
        }
        if (
          !item.content ||
          item.content.trim() === "" ||
          item.content === "<p><br></p>"
        ) {
          isValid = false;
          firstErrorIndex = i;
          firstErrorMessage = `第${i + 1}个行程点：请输入行程内容`;
          break;
        }
      }
    } catch (error) {
      isValid = false;
      if (firstErrorIndex === -1) {
        firstErrorIndex = i;
        // 提取具体的错误信息
        if (error && typeof error === "object") {
          const errorFields = Object.keys(error);
          if (errorFields.length > 0) {
            const firstField = errorFields[0];
            if (error[firstField] && error[firstField][0]) {
              firstErrorMessage = `第${i + 1}个行程点：${error[firstField][0].message}`;
            }
          }
        }
      }
      break;
    }
  }

  if (!isValid) {
    buttonLoading.value = false;
    ElMessage.error(
      firstErrorMessage || `第${firstErrorIndex + 1}个行程信息填写有误，请检查`
    );
    return;
  }

  const tripData = tripList.value.map(it => {
    return {
      content: it.content,
      gapDays: it.gapDays,
      time: it.time,
      title: it.title,
      lecturerId: it.lecturerId,
      complexId: it.complexId
    };
  });
  // console.log("🦄-----tripData-----", tripData);
  const operateLog = {
    operateLogType: "COURSE_MANAGEMENT",
    operateType: `将“${props.periodName}”课期中的课期行程保存在草稿箱`
    // operatorTarget: form.value.name,
  };
  const params = {
    draftId: Number(route.query.draftId) || Number(useCourseStore.draftId),
    draftItineraries: tripData
  };
  const newList = removeEmptyValues(params);
  let [err, res] = await to(itinerarySaveDraftItinerary(newList, operateLog));
  // console.log("🐬-----err, res-----", err, res);
  if (res) {
    if (res.code === 30040) {
      ElMessage.error(`${res.msg},保存失败。`);
      buttonLoading.value = false;
    } else if (res.code === 200) {
      tripStore.saveBasePlace({});
      tripStore.saveLecturerData({});
      saveCurrentFormToStore();
      ElMessage.success("当前资料已保存到草稿箱");
      draftIdTripApi();
      CoursePeriodIdTripApi();
      buttonLoading.value = false;
    }
  } else {
    ElMessage.error(`保存失败。`);
    buttonLoading.value = false;
  }
};

// 删除 草稿箱 Api
const deleteDraft = async () => {
  let operateLogDraft = {
    operateLogType: "COURSE_MANAGEMENT",
    operateType: route.query.draftId
      ? `删除了草稿id为“${route.query.draftId}”的草稿数据`
      : `删除了草稿id为“${useCourseStore.draftId}”的草稿数据`
  };
  const [err, res] = await to(
    draftDelete(
      { id: Number(route.query.draftId) || Number(useCourseStore.draftId) },
      operateLogDraft
    )
  );
  if (res.code === 200) {
    ElMessage.success("删除成功");
  } else {
    ElMessage.error("删除失败");
  }
};
// 退出
const backEvt = (val, it) => {
  // console.log("🍭-----val, it-----", val, it);
  if (val === "exit") {
    let titlt =
      it.id === 1
        ? `请确认是否清除当前编辑和提交的所有资料，并删除当前编辑资料在草稿箱中对应的草稿条目`
        : `请确认是否将当前编辑和提交的所有资料保存到草稿箱，并退出编辑页面`;
    ElMessageBox.confirm(`${titlt}`, `退出并${it.name}`, {
      confirmButtonText: "确定",
      cancelButtonText: "取消"
    }).then(() => {
      if (it.id === 1) {
        deleteDraft();
        if (route.query.type === "draft") {
          router.replace("/course/drafts");
        } else if (route.query.type === "edite") {
          router.replace("/course/courseDetails");
        } else {
          router.go(-1);
        }
      } else if (it.id === 2) {
        saveApi();
        if (route.query.type === "draft") {
          router.replace("/course/drafts");
        } else if (route.query.type === "edite") {
          router.replace("/course/courseDetails");
        } else {
          router.go(-1);
        }
      }
    });
  } else {
    if (route.query.fromPage === "courseDetail") {
      router.replace({
        path: "/course/courseDetails",
        query: { id: route.query.courseId }
      });
    } else if (route.query.fromPage === "currentDetail") {
      router.replace({
        path: "/course/courseDetails/currentDetails",
        query: {
          periodId: route.query.periodId,
          courseId: route.query.courseId
        }
      });
    }
  }
};

// 课程详情-编辑集合
const isOpenTime = ref("");
const coursePeriodFindApi = async () => {
  const params = {
    id: route.query.periodId
  };
  const newList = removeEmptyValues(params);
  let [err, res] = await to(coursePeriodFind(newList));
  if (res) {
    CoursePeriodId.value = res?.data;
    isOpenTime.value = res?.data?.openTime || 1;
    openTimeValue.value = res?.data?.openTime;
    openTimeNian.value = formatTimestamp(res?.data?.openTime);
    findByCoursePeriodApi();
  }
};
// 编辑 获取行程介绍数据 api
const findByCoursePeriodApi = async () => {
  const params = {
    coursePeriodId: route.query.periodId
  };
  const newList = removeEmptyValues(params);
  let [err, res] = await to(findByCoursePeriodId(newList));
  if (res) {
    tripList.value = res?.data?.map(it => {
      return {
        content: it.content,
        gapDays: calculateDays(it.startTime, isOpenTime.value),
        id: it.id,
        startTime: it.startTime,
        time: dayjs(it.startTime).format("HH:mm:ss"),
        title: it.title,
        complexId: it.complexDTO?.id,
        lecturerId: it.organizationAdminDTO?.id
      };
    });
  }
};
// 编辑 保存并返回和保存按钮 api
const saveEvt = async text => {
  buttonLoading.value = true;
  let operateLog = {
    operateLogType: "COURSE_MANAGEMENT",
    operateType: `编辑了“${CoursePeriodId.value.name}”课期的行程点`
  };

  if (Array.isArray(tripList.value) && tripList.value.length === 0) {
    buttonLoading.value = false;
    return ElMessage.error({
      type: "error",
      message: "请新增行程点"
    });
  }
  let isValid = true;
  let firstErrorIndex = -1;
  let firstErrorMessage = "";

  // 遍历所有表单进行校验
  for (let i = 0; i < tripList.value.length; i++) {
    try {
      // 确保表单引用存在
      if (formRefs.value[i]) {
        await formRefs.value[i].validate();
      } else {
        // 如果表单引用不存在，手动检查字段
        const item = tripList.value[i];
        if (!item.time) {
          isValid = false;
          firstErrorIndex = i;
          firstErrorMessage = `第${i + 1}个行程点：请选择时间`;
          break;
        }
        const itemDateTime = dayjs(item.time).valueOf();
        if (itemDateTime < openTimeValue.value) {
          isValid = false;
          firstErrorIndex = i;
          firstErrorMessage = `第${i + 1}个行程点：时间不能早于开课时间`;
          break;
        }
        if (!item.complexId) {
          isValid = false;
          firstErrorIndex = i;
          firstErrorMessage = `第${i + 1}个行程点：请选择实践点`;
          break;
        }
        if (!item.lecturerId) {
          isValid = false;
          firstErrorIndex = i;
          firstErrorMessage = `第${i + 1}个行程点：请选择讲师`;
          break;
        }
        if (!item.title || item.title.trim() === "") {
          isValid = false;
          firstErrorIndex = i;
          firstErrorMessage = `第${i + 1}个行程点：请编辑行程主题`;
          break;
        }
        if (
          !item.content ||
          item.content.trim() === "" ||
          item.content === "<p><br></p>"
        ) {
          isValid = false;
          firstErrorIndex = i;
          firstErrorMessage = `第${i + 1}个行程点：请编辑行程内容`;
          break;
        }
      }
    } catch (error) {
      isValid = false;
      if (firstErrorIndex === -1) {
        firstErrorIndex = i;
        // 提取具体的错误信息
        if (error && typeof error === "object") {
          const errorFields = Object.keys(error);
          if (errorFields.length > 0) {
            const firstField = errorFields[0];
            if (error[firstField] && error[firstField][0]) {
              firstErrorMessage = `第${i + 1}个行程点：${error[firstField][0].message}`;
            }
          }
        }
      }
      break;
    }
  }

  if (!isValid) {
    buttonLoading.value = false;
    ElMessage.error(
      firstErrorMessage || `第${firstErrorIndex + 1}个行程信息填写有误，请检查`
    );
    return;
  }

  const arrData = ref([]);
  tripList.value.map((it, index) => {
    return arrData.value.push({
      content: it.content,
      title: it.title,
      startTime: calculate(isOpenTime.value, it.time, it.gapDays),
      complexId: it.complexId,
      lecturerId: it.lecturerId
    });
  });

  let params = {
    coursePeriodId: route.query.periodId,
    itinerarys: arrData.value
  };
  const newList = removeEmptyValues(params);
  // console.log("🎁newList------------------------------>", newList);
  // return;
  let [err, res] = await to(itineraryUpdateV2(newList, operateLog));
  if (res.code === 200) {
    tripStore.saveBasePlace({});
    tripStore.saveLecturerData({});
    saveCurrentFormToStore();
    ElMessage({
      type: "success",
      message: "编辑成功"
    });
    if (text === "saveRet") {
      router.back();
    }
    buttonLoading.value = false;
  } else {
    buttonLoading.value = false;
    ElMessage({
      type: "error",
      message: `编辑失败，${res.msg}`
    });
  }
  buttonLoading.value = false;
};
// 领队讲师查询 type为2讲师 type为3领队
const leaderFindApi = async type => {
  const params = {
    roleId: type
  };
  let [err, res] = await requestTo(leaderLecturerFind(params));
  if (res) {
    if (type == 2) {
      teacherOptions.value = res.map(it => {
        return {
          ...it,
          label: it.name,
          value: it.id
        };
      });
    }
  } else {
    console.log("🌵-----err-----", err);
  }
};
//  实践点查询不分页
const complexIdApi = async () => {
  let [err, res] = await requestTo(complexId());
  if (res) {
    placeOptions.value = res.map(it => {
      return {
        ...it,
        label: it.name,
        value: it.id
      };
    });
  }
};
onMounted(() => {
  if (route.query.create === "createTrip") {
    updateFormFromStore();
  } else {
    if (route.query.type === "edite") {
      //draft  edite
      if (route.query.periodId) {
        coursePeriodFindApi();
      }
      tripList.value = [];
    } else {
      draftIdTripApi();
      CoursePeriodIdTripApi();
    }
  }
  leaderFindApi(2);
  complexIdApi();
  // startAutoSave()
});

let time1 = openTimeValue.value;
const pickerChange = (value, index) => {
  if (!value.timeA) {
    tripList.value[index].time = "";
    // tripList.value[index].timeA = "";
    return ElMessage({
      message: "请选择时间点",
      type: "error"
    });
  }
  const startTImes = dayjs(value.timeA).format("HH:mm:ss");
  const startTime = openTimeNian.value + " " + startTImes;
  let s = new Date(startTime).getTime();
  if (value.gapDays > 1) {
    tripList.value[index].time = startTImes;
  } else {
    if (openTimeValue.value > s) {
      tripList.value[index].time = "";
      tripList.value[index].timeA = "";
      return ElMessage({
        message: "时间不能早于开课时间",
        type: "error"
      });
    } else {
      tripList.value[index].time = startTImes;
    }
  }
};
const numberChange = (value, index) => {
  if (
    value.gapDays == "" ||
    value.gapDays === null ||
    value.gapDays === undefined
  ) {
    value.gapDays = 1;
  }
  Math.round(value.gapDays);
  if (value.timeA) {
    const startTImes = dayjs(value.timeA).format("HH:mm:ss");
    const startTime = openTimeNian.value + " " + startTImes;
    let s = new Date(startTime).getTime();
    if (value.gapDays > 1) {
      tripList.value[index].time = startTImes;
    } else {
      if (openTimeValue.value > s) {
        tripList.value[index].time = "";
        tripList.value[index].timeA = "";
        return ElMessage({
          message: "时间不能早于开课时间",
          type: "error"
        });
      } else {
        tripList.value[index].time = startTImes;
      }
    }
  }
};

const timest = val => {
  const timeRegex = /^(?:[01]\d|2[0-3]):[0-5]\d:[0-5]\d$/;
  if (!timeRegex.test(val)) return;
  let timeStr = val;
  let timeParts = timeStr.split(":"); // 分割时间字符串为小时、分钟、秒
  let hours = parseInt(timeParts[0], 10);
  let minutes = parseInt(timeParts[1], 10);
  let seconds = parseInt(timeParts[2], 10);
  let today = new Date(); // 获取当前日期和时间
  today.setHours(hours, minutes, seconds, 0); // 设置小时、分钟、秒和毫秒
  let timestamp = today.getTime(); // 获取时间戳
  // console.log(timestamp); // 输出时间戳
  return timestamp;
};

function formatTimestamp(timestamp) {
  const date = new Date(timestamp);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0"); // 月份从 0 开始
  const day = String(date.getDate()).padStart(2, "0");
  return `${year}-${month}-${day}`;
}
/**
 * 计算两个时间戳相差的整数天数
 * @param timestamp1 第一个时间戳（毫秒）
 * @param timestamp2 第二个时间戳（毫秒）
 * @returns 相差的整数天数（绝对值）
 */
function calculateDays(timestamp1, timestamp2) {
  const timestamp = timestamp2; // 给定的时间戳
  const date = new Date(timestamp); // 转为 Date 对象
  date.setHours(0, 0, 0, 0); // 设置为当天的 00:00:00
  const startOfDayTimestamp = date.getTime(); // 获取时间戳

  const timeDifference = Math.abs(startOfDayTimestamp - timestamp1);
  const daysDifference = Math.ceil(timeDifference / (24 * 60 * 60 * 1000));
  return daysDifference;
}

function calculate(timestamp1, timestamp2, timestamp3) {
  const time55 = timestamp1;
  const date = new Date(time55);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0"); // "05"
  const day = String(date.getDate()).padStart(2, "0"); // "07"
  const itemdate = `${year}-${month}-${day} ` + timestamp2;
  const tian = timestamp3 - 1;
  const timestamp = dayjs(itemdate).add(tian, "day").valueOf();
  return timestamp;
}

const nextStepApiatt = async val => {
  const tre = ref(false);
  const type = deepEqualArrays(tripList.value, editCopyClone.value);
  if (type === true) {
    tre.value = true;
    buttonLoading.value = false;
  } else {
    if (Array.isArray(tripList.value) && tripList.value.length === 0) {
      buttonLoading.value = false;
      return ElMessage.error({
        type: "error",
        message: "请新增行程点"
      });
    }
    let isValid = true;
    let firstErrorIndex = -1;
    let firstErrorMessage = "";

    // 遍历所有表单进行校验
    for (let i = 0; i < tripList.value.length; i++) {
      try {
        // 确保表单引用存在
        if (formRefs.value[i]) {
          await formRefs.value[i].validate();
        } else {
          // 如果表单引用不存在，手动检查字段
          const item = tripList.value[i];

          // 校验天数
          if (!item.gapDays || item.gapDays <= 0) {
            isValid = false;
            firstErrorIndex = i;
            firstErrorMessage = `第${i + 1}个行程点：请输入正确的天数`;
            break;
          }

          // 校验时间
          if (!item.time) {
            isValid = false;
            firstErrorIndex = i;
            firstErrorMessage = `第${i + 1}个行程点：请选择时间`;
            break;
          }

          // 校验时间是否早于开课时间
          const currentGapDays = item.gapDays || 1;
          const checkDateTime =
            dayjs(openTimeNian.value)
              .add(currentGapDays - 1, "day")
              .format("YYYY-MM-DD") +
              " " +
              item.time;
          const itemDateTime = dayjs(checkDateTime).valueOf();
          if (itemDateTime < openTimeValue.value) {
            isValid = false;
            firstErrorIndex = i;
            firstErrorMessage = `第${i + 1}个行程点：时间不能早于开课时间`;
            break;
          }

          // 校验实践点
          if (!item.complexId) {
            isValid = false;
            firstErrorIndex = i;
            firstErrorMessage = `第${i + 1}个行程点：请选择实践点`;
            break;
          }

          // 校验讲师
          if (!item.lecturerId) {
            isValid = false;
            firstErrorIndex = i;
            firstErrorMessage = `第${i + 1}个行程点：请选择讲师`;
            break;
          }

          // 校验主题
          if (!item.title || item.title.trim() === "") {
            isValid = false;
            firstErrorIndex = i;
            firstErrorMessage = `第${i + 1}个行程点：请输入行程主题`;
            break;
          }

          // 校验内容
          if (
            !item.content ||
            item.content.trim() === "" ||
            item.content === "<p><br></p>"
          ) {
            isValid = false;
            firstErrorIndex = i;
            firstErrorMessage = `第${i + 1}个行程点：请输入行程内容`;
            break;
          }
        }
      } catch (error) {
        isValid = false;
        if (firstErrorIndex === -1) {
          firstErrorIndex = i;
          // 提取具体的错误信息
          if (error && typeof error === "object") {
            const errorFields = Object.keys(error);
            if (errorFields.length > 0) {
              const firstField = errorFields[0];
              if (error[firstField] && error[firstField][0]) {
                firstErrorMessage = `第${i + 1}个行程点：${error[firstField][0].message}`;
              }
            }
          }
        }
        break;
      }
    }

    if (!isValid) {
      buttonLoading.value = false;
      ElMessage.error(
        firstErrorMessage ||
        `第${firstErrorIndex + 1}个行程信息填写有误，请检查`
      );
      return;
    }
    const tripData = tripList.value.map(it => {
      return {
        content: it.content,
        gapDays: it.gapDays,
        time: it.time,
        title: it.title,
        lecturerId: it.lecturerId,
        complexId: it.complexId
      };
    });
    const params = {
      draftId: Number(route.query.draftId) || Number(useCourseStore.draftId),
      draftItineraries: tripData
    };
    const operateLog = {
      operateLogType: "COURSE_MANAGEMENT",
      operateType: `将“${props.periodName}”课期中的课期行程保存在草稿箱`
      // operatorTarget: form.value.name,
    };
    const newList = removeEmptyValues(params);
    let [err, res] = await to(ItineraryNextDraftItinerary(newList, operateLog));
    // console.log("🍧-----err, res-----", err, res);
    if (res) {
      if (res.code === 30040) {
        ElMessage.error(`${res.msg},保存失败。`);
        buttonLoading.value = false;
        tre.value = false;
      } else if (res.code === 200) {
        ElMessage.success("当前资料已保存到草稿箱");
        tripStore.saveBasePlace({});
        tripStore.saveLecturerData({});
        saveCurrentFormToStore();
        buttonLoading.value = false;
        tre.value = true;
      }
    } else {
      buttonLoading.value = false;
      tre.value = false;
    }
  }
  return tre.value;
};
// 存储行程信息
const saveCurrentFormToStore = () => {
  // console.log(
  //   "🌈tripList.value------------------------------>",
  //   tripList.value
  // );
  tripStore.saveDraftTrip(tripList.value);
};
const updateFormFromStore = () => {
  tripList.value = tripStore.draftTrip;
  // console.log('🐬tripStore.draftTrip------------------------------>',tripStore.draftTrip);
  // console.log(
  //   "🦄tripStore.basePlace.id------------------------------>",
  //   tripStore.basePlace.id
  // );
  // console.log(
  //   "🎁tripStore.lecturerData.id------------------------------>",
  //   tripStore.lecturerData.id
  // );

  if (!isEmpty(route.query.complexIndex) && tripStore.basePlace.id) {
    tripList.value[+route.query.complexIndex].complexId =
      tripStore.basePlace.id;
  }
  if (!isEmpty(route.query.lecturerIndex) && tripStore.lecturerData.id) {
    tripList.value[+route.query.lecturerIndex].lecturerId =
      tripStore.lecturerData.id;
  }
};
const clearFromStore = () => {
  tripStore.saveDraftTrip([]);
  tripStore.saveBasePlace({});
  tripStore.saveLecturerData({});
};
// 点击新建实践点讲师
const addNewOption = (index, item) => {
  // console.log("🌵item------------------------------>", index, item);
  tripStore.saveBasePlace({});
  tripStore.saveLecturerData({});
  saveCurrentFormToStore();
  if (item === "complex") {
    router.push({
      path: "/courseCreate/baseAdd",
      query: {
        type: route.query.type,
        id: route.query.id,
        name: route.query.name,
        courseId: route.query.courseId,
        termNumber: route.query.termNumber,
        draftId: route.query.draftId || useCourseStore.draftId,
        complexIndex: index,
        periodId: route.query.periodId
      }
    });
  } else if (item === "lecturer") {
    router.push({
      path: "/courseCreate/lecturerCreate",
      query: {
        type: "new",
        text: "teacher",
        roleId: 2,
        type: route.query.type,
        courseId: route.query.courseId,
        id: route.query.id,
        name: route.query.name,
        termNumber: route.query.termNumber,
        draftId: route.query.draftId || useCourseStore.draftId,
        lecturerIndex: index,
        periodId: route.query.periodId
      }
    });
  }
};
router.afterEach((to, from) => {
  if (whitePath.includes(to.path)) {
    clearFromStore();
  }
});

// 用于草稿箱点击基础信息校验
defineExpose({
  nextStepApiatt
});
function isJSONString(str) {
  try {
    JSON.parse(str);
    return true;
  } catch (e) {
    return false;
  }
}

let count = ref(3);
const textLoading = ref(false);
const loading = ref(false);
// 智能填充行程内容的 确认+取消按钮
const editorLoading = ref(false);
const valueHtmlv1 = ref("");
const editorEvt = async val => {
  if (val === "confirm") {
    loading.value = true;
    const text = valueHtmlv1.value
      .replace(/\n/g, "\\n")
      .replace(/\t/g, "\\t")
      .replace(/\r/g, "\\r")
      .replace(/<br\s*\/?>/gi, "\\n");
    const params = {
      description: text,
      questionType: "ITINERARY"
    };
    const newList = removeEmptyValues(params);
    // console.log("🎉-----newList-------------------", newList);
    // tripList.value = []
    try {
      // textLoading.value = true
      // count.value = 3
      let { code, data } = await questionAsk(newList);
      if (code == 200 && data?.id) {
        // const intervalId = setInterval(() => {
        //   count.value--;
        //   console.log(`第 ${count.value} 次执行`);
        //   if (count.value <= 0) {
        //     textLoading.value = false
        //     clearInterval(intervalId); // 停止计时器
        //   }
        // }, 1000);
        textLoading.value = true;
        const asyncFn = await vETgetAsyncTask(data?.id);

        handleCancel = asyncFn.stop;
        const task = await asyncFn.task;
        if (task.data.complete && task.data.success) {
          let resData = {};
          // console.log("🦄-----task-----", task);
          if (task.data.res) {
            if (isJSONString(task.data.res)) {
              resData = JSON.parse(task.data.res);
              // console.log("🌵-----resData----成功-", resData);
              tripList.value = resData.map((it, ind) => {
                return {
                  content: it.content,
                  gapDays: it.gapDays,
                  id: ind + 1,
                  timeA: timest(it.time),
                  time: it.time,
                  title: it.title
                };
              });
              ElMessage.success("智能填充成功");
            } else {
              let t = `填充失败，请提供完整的行程信息，包含行程标题、第几天、具体时间及行程内容。`;
              ElMessage.error(t);
            }
            loading.value = false;
          } else {
            ElMessage.error(task.data.errMsg);
            loading.value = false;
          }
        }
      }
    } catch (error) {
      loading.value = false;
      ElMessage.error("智能填充失败");
    }
  } else {
    valueHtmlv1.value = "";
  }
};
const handleCancelFn = () => {
  loading.value = false;
  textLoading.value = false;
  handleCancel?.();
};
// // 自动保存
// const autoSaveInterval = ref(null); // 新增：用于存储定时器引用
// const startAutoSave = () => {
//   clearInterval(autoSaveInterval.value); // 清除已有定时器以防重复
//   autoSaveInterval.value = setInterval(
//     () => {
//       upperBelowEvt('save')
//     },
//     5 * 60 * 1000
//   ); // 每5分钟执行一次
// };
// onUnmounted(() => {
//   clearInterval(autoSaveInterval.value); // 组件卸载时清除定时器
// });
</script>

<template>
  <div class="scheduling">
    <div v-if="loading" class="custom-loading">
      <div class="loading-content">
        <el-icon class="is-loading" color="#409efc" :size="48">
          <Loading />
        </el-icon>

        <p>智能填充中，请耐心等待...</p>
        <el-button
          v-if="textLoading"
          color="rgba(255, 255, 255, 0.3)"
          round
          size="small"
          @click="handleCancelFn"
        >
          <!-- <span v-if="textLoading">（{{ count }}）</span> -->
          取消
        </el-button>
      </div>
    </div>

    <div class="box">
      <div class="timelinebox">
        <div style="display: flex; justify-content: end">
          <el-button type="primary mb-4" @click="isDialog('newly')">
            新增行程点
          </el-button>
        </div>
        <el-timeline class="content">
          <el-timeline-item
            v-for="(item, index) in tripList"
            :key="item.id"
            :timestamp="item.timestamp"
          >
            <!-- <div class="inebox">
              <div class="boxpicker">
                <div class="m-w tian">
                  第
                  <el-input-number
                    v-model.trim="item.gapDays"
                    style="width: 120px"
                    type="number"
                    :min="1"
                    :max="100"
                    @change="numberChange(item, index)"
                  />
                  天
                </div>
                <div>
                  <el-time-picker
                    v-model="item.timeA"
                    placeholder="请选择时间点"
                    style="width: 160px"
                    @change="pickerChange(item, index)"
                  />
                </div>
              </div>
              <el-button
                type="warning"
                :icon="Delete"
                circle
                @click="deteleEvt(item, index)"
              />
            </div> -->
            <el-form
              :ref="
                el => {
                  if (el) formRefs[index] = el;
                }
              "
              :model="item"
              :rules="getRules(index)"
              class="schedule-form"
              label-width="auto"
              :validate-on-rule-change="false"
            >
              <el-row :gutter="10">
                <el-col :span="7">
                  <el-form-item prop="gapDays">
                    <div class="m-w tian">
                      第
                      <el-input-number
                        v-model.trim="item.gapDays"
                        style="width: 120px"
                        type="number"
                        :min="1"
                        :max="100"
                        @change="numberChange(item, index)"
                      />
                      天
                    </div>
                  </el-form-item>
                </el-col>

                <el-col :span="8">
                  <el-form-item label="时间" prop="time">
                    <el-time-picker
                      v-model="item.time"
                      placeholder="选择时间"
                      value-format="HH:mm:ss"
                      style="width: 100%"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-button
                    type="danger"
                    :icon="Delete"
                    circle
                    @click="deteleEvt(item, index)"
                  />
                  <el-form-item />
                </el-col>
              </el-row>
              <el-form-item label="实践点：" prop="complexId">
                <div style="display: flex">
                  <el-select
                    v-model="item.complexId"
                    placeholder="请选择实践点"
                    style="width: 300px; margin-right: 10px"
                  >
                    <el-option
                      v-for="option in placeOptions"
                      :key="option.value"
                      :label="option.label"
                      :value="option.value"
                    />
                  </el-select>
                  <el-button
                    type="primary"
                    class="add-option-btn"
                    @click="addNewOption(index, 'complex')"
                  >
                    新建实践点
                  </el-button>
                </div>
              </el-form-item>

              <el-form-item label="讲师：" prop="lecturerId">
                <el-select
                  v-model="item.lecturerId"
                  placeholder="请选择讲师"
                  style="width: 300px; margin-right: 10px"
                >
                  <el-option
                    v-for="option in teacherOptions"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                  />
                </el-select>
                <el-button
                  type="primary"
                  class="add-option-btn"
                  @click="addNewOption(index, 'lecturer')"
                >
                  新建讲师
                </el-button>
              </el-form-item>

              <el-form-item label="行程主题：" prop="title">
                <div
                  style="
                    display: flex;
                    justify-content: space-between;
                    width: 390px;
                  "
                >
                  <div>
                    {{ item.title }}
                  </div>
                  <el-button
                    type="primary"
                    plain
                    @click="isDialog('edit', item, index)"
                  >
                    编辑<el-icon style="margin-left: 6px">
                      <Edit />
                    </el-icon>
                  </el-button>
                </div>
              </el-form-item>

              <el-form-item label="行程内容：" prop="content">
                <div v-html="item.content" />
              </el-form-item>
            </el-form>

            <!-- <div class="item_content">
              <div v-html="item.content" />
            </div> -->
          </el-timeline-item>
        </el-timeline>

        <el-dialog
          v-model="dialogFormVisible"
          :title="`${dialogTitle}行程点`"
          width="70%"
        >
          <el-form
            ref="ruleFormRef"
            :model="form"
            :rules="rules"
            label-width="100px"
          >
            <el-form-item label="行程点标题" prop="title">
              <el-input
                v-model.trim="form.title"
                placeholder="请输入行程标题"
                style="width: 340px"
                :maxlength="50"
                show-word-limit
                type="text"
              />
            </el-form-item>
          </el-form>
          <div class="editer">
            <RichEditor
              v-model="valueHtml"
              height="260px"
              :excludeKeys="['headerSelect', 'fontSize', 'lineHeight']"
              :lineHeight="2"
            />
          </div>
          <template #footer>
            <div class="dialog-footer">
              <el-button @click="cancel">取消</el-button>
              <el-button
                type="primary"
                :loading="submitLoading"
                @click="addItineraryCreate(ruleFormRef)"
              >
                保存
              </el-button>
            </div>
          </template>
        </el-dialog>
      </div>

      <div class="inputbox">
        <div class="title">智能填充行程内容</div>
        <div class="vueEditor">
          <el-input
            v-model.trim="valueHtmlv1"
            type="textarea"
            placeholder="请输入..."
          />
          <div
            v-if="valueHtmlv1 !== ''"
            style="display: flex; justify-content: end; padding: 10px 0px"
          >
            <el-button @click="editorEvt('cancel')"> 取消 </el-button>
            <el-button
              :loading="editorLoading"
              type="primary"
              @click="editorEvt('confirm')"
            >
              确认
            </el-button>
          </div>
        </div>
      </div>
    </div>
    <div class="butt">
      <div v-if="props.isNewEdit === 'new'">
        <el-dropdown>
          <el-button style="margin-right: 10px">退出</el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item
                v-for="it in btnData"
                :key="it.id"
                @click="backEvt('exit', it)"
              >
                {{ it.name }}
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
        <el-button
          type="primary"
          :loading="buttonLoading"
          @click="upperBelowEvt('save')"
        >
          保存草稿箱
        </el-button>
        <el-button type="primary" @click="upperBelowEvt('upper')">
          上一步
        </el-button>
        <el-button
          type="primary"
          :loading="buttonLoading"
          @click="upperBelowEvt('below')"
        >
          下一步
        </el-button>
      </div>
      <div v-if="props.isNewEdit === 'edit'">
        <el-button @click="router.go(-1)">返回</el-button>
        <el-button
          type="primary"
          :loading="buttonLoading"
          @click="saveEvt('saveRet')"
        >
          保存并返回
        </el-button>
        <el-button type="primary" :loading="buttonLoading" @click="saveEvt">
          保存
        </el-button>
      </div>
      <el-button type="primary" @click="aiNewPage(infoShowEnName)">
        AI课程设计
      </el-button>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.custom-loading .circular {
  margin-right: 6px;
  width: 18px;
  height: 18px;
  animation: loading-rotate 2s linear infinite;
}
.custom-loading .circular .path {
  animation: loading-dash 1.5s ease-in-out infinite;
  stroke-dasharray: 90, 150;
  stroke-dashoffset: 0;
  stroke-width: 2;
  stroke: var(--el-button-text-color);
  stroke-linecap: round;
}

.custom-loading {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  z-index: 9999;
  display: flex;
  justify-content: center;
  align-items: center;
}
.loading-content {
  text-align: center;
  color: #fff;
}
.loading-content i {
  font-size: 30px;
  margin-bottom: 10px;
}
.loading-content p {
  margin: 10px 0;
}
.el-icon-loading {
  width: 100px;
  height: 100px;
}

.scheduling {
  height: 100%;
  width: 100%;

  .box {
    width: 100%;
    height: calc(100% - 63px);
    position: relative;
    display: flex;
    margin-bottom: 30px;

    .timelinebox {
      width: 60%;
      .content {
        width: 100%;
        height: calc(100vh - 372px);
        overflow-y: auto;
        padding: 10px 0 10px 10px;
        box-sizing: border-box;
        scrollbar-width: none;
        -ms-overflow-style: none;
        .timeLine_title {
          width: 100%;
          display: flex;
          align-items: center;
          gap: 20px;
        }
        .inebox {
          display: flex;
          justify-content: space-between;
          align-items: center;
          height: 100%;
          width: 100%;
          .boxpicker {
            display: flex;
            // justify-content: space-between;
            align-items: center;
            .tian {
              margin-right: 10px;
            }
          }
        }

        .item_content {
          margin-top: 20px;
          width: 100%;
          padding-left: 30px;
          // 添加表格样式
          :deep(table) {
            border-collapse: collapse;
            td,
            th {
              border: 1px solid #a7a6a6;
              min-width: 50px;
              text-align: center;
            }
          }
        }
      }
      .content::-webkit-scrollbar {
        display: none;
      }
    }
    .inputbox {
      width: 40%;
      margin-left: 20px;
      .title {
        padding: 10px 0px;
        min-width: fit-content;
        font-size: 15px;
        font-weight: bold;
      }
    }
  }

  .butt {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}

.editer {
  height: 350px;
  overflow-y: auto;
  margin-bottom: 5px;
}

.vueEditor {
  width: 100%;
  height: calc(100% - 42px);
  border-radius: 4px;
  padding: 12px 0px 0px;
  // border: solid 1px var(--el-border-color);
}

.m-w {
  min-width: fit-content;
  font-size: 16px;
  font-weight: bold;
}

:deep(.el-textarea .el-textarea__inner) {
  height: calc(100vh - 410px) !important;
}
</style>
