<script setup>
import { onMounted, ref, watch, computed, nextTick } from "vue";
import { PlusTable, useTable, PlusDialogForm } from "plus-pro-components";
import { useRoute, useRouter } from "vue-router";
import {
  nowledgefindById,
  nowledgeCreateOrUpdate,
  findCourseKnowledgePointByGrade,
  findCourseKnowledgePointBySubject,
  findCourseMaterialVersion,
  findCourseDataCourse,
  findCourseDataCourseBySubject,
  findCourseKnowledgePoint,
  saveDraftCourseKnowledge,
  // findCourseKnowledgePointByParentId,
  deleteCourseKnowledgePoint2,
  findById2,
  nowledgeCreateOrUpdate2
} from "@/api/period.js";
import {
  draftCourseKnowledgePointFindByDraftId,
  draftCourseKnowledgePoint,
  saveDraftCourseKnowledgePointNext,
  findByIdDraftCourseKnowledgePoint,
  deleteDraftCourseKnowledgePoint,
  draftDelete
} from "@/api/drafts.js";
// import RichEditor from "@/components/Base/RichEditor.vue";
import { requestTo } from "@/utils/http/tool";
// import { number } from "echarts";
import { ElMessage, ElMessageBox } from "element-plus";
// import DescriptionList from "./descriptionList.vue";
import { to, deepClone } from "@iceywu/utils";
import { aiNewPage } from "@/utils/aiTool.js";
import { courseStore } from "@/store/modules/course.js";
import { v4 as uuidv4 } from "uuid";

const props = defineProps({
  infoShow: {
    type: String,
    default: ""
  },
  draftId: {
    type: Number,
    default: 0
  },
  periodName: {
    type: String,
    default: "未命名"
  },
  infoShowEnName: {
    type: String,
    default: "foundation"
  },
  multiple: {
    type: Boolean, // 使用构造函数
    default: false
  }
});

const emites = defineEmits(["baseInfo"]);

// API请求函数
const fetchVersionsData = async (val1, val2) => {
  try {
    const response = await findCourseMaterialVersion({
      stageId: val1,
      subjectId: val2
    });
    return response.data || [];
  } catch (error) {
    console.error("获取版本数据失败:", error);
    return [];
  }
};

const fetchChapterData = async textbookId => {
  try {
    const response = await findCourseDataCourse({ textbookId });
    return response.data || [];
  } catch (error) {
    console.error("获取章节数据失败:", error);
    return [];
  }
};

// 递归转换章节数据为级联选择器节点
const transformChapterData = chapters => {
  return chapters.map(chapter => ({
    id: chapter.id,
    name: chapter.name,
    leaf: !chapter.children || chapter.children.length === 0,
    children:
      chapter.children && chapter.children.length > 0
        ? transformChapterData(chapter.children)
        : null,
    chapterData: chapter,
    nodeType: "chapter"
  }));
};

const materialCache = new Map();

onMounted(() => {
  findCourseKnowledgePointByGradeList();
  findCourseKnowledgePointBySubjectList();
  findCourseKnowledgePointByCityList();
});
const useCourseStore = courseStore();
const router = useRouter();
const route = useRoute();
const valueHtml = ref();
const operateLog = ref({});
// 判断api类型
const getApiType = (val, isEdit) => {
  let res = "";
  // case "课期知识点":
  res = isEdit ? nowledgeCreateOrUpdate : nowledgefindById;
  operateLog.value = {
    operateLogType: "COURSE_MANAGEMENT",
    operateType: `编辑了“${props.periodName}”课期中的课程知识点`
  };
  //   break;
  return res;
};
const operateLogDraft = ref({});
// 判断api类型草稿箱
const getApiDraftType = (val, isEdit, obj) => {
  let res = "";
  res = isEdit
    ? draftCourseKnowledgePoint
    : draftCourseKnowledgePointFindByDraftId;
  if (obj) {
    operateLogDraft.value = obj;
  } else {
    operateLogDraft.value = {
      operateLogType: "COURSE_MANAGEMENT",
      operateType: `将“${props.periodName}”课期中的课期知识点保存在草稿箱`
    };
  }
  return res;
};
// 判断日志输出
const getLogType = val => {
  let res = {};
  // case "课期知识点":
  res = {
    operateLogType: "COURSE_MANAGEMENT",
    operateType: `编辑了“${props.periodName}”课期中的课程知识点`
  };
  return res;
};

const valueHtmlClone = ref("");
const tableData2 = ref([]);
// 查询详情
const getIntroductionfindById = async () => {
  const params = {
    coursePeriodId: Number(route.query.periodId)
  };
  let api = getApiType(props.infoShow, false);
  let [err, res] = await to(api(params));
  if (res.code === 200) {
    // valueHtml.value = res?.data?.content || "";
    // valueHtmlClone.value = deepClone(valueHtml.value);
    // tableData2.value = [...res.data];
    tableData.value = await tableDataInit(tableData.value, res.data);
  } else {
  }
  if (err) {
  }
};
// 草稿箱查询详情
const getdraftfindById = async () => {
  const params = {
    draftId: Number(route.query.draftId) || Number(useCourseStore.draftId)
  };
  let api = getApiDraftType(props.infoShow, false);

  let [err, res] = await to(api(params));
  if (res.code === 200) {
    // tableData2.value = [...res.data];
    tableData.value = await tableDataInit(tableData.value, res.data);
  } else {
  }
  if (err) {
  }
};
const state = ref(false);
// 中间函数处理页面数据回显
const tableDataInit = async (obj, objRes) => {
  obj = objRes;
  // let echData = null;
  // let ttData = null;
  obj.map(async (item, i) => {
    values.value.stageId = item.stage?.id;
    values.value.subjectId = item.subject?.id;
    item.stage = item.stage?.name;
    item.subject = item.subject?.name;
    item.switch =
      item.textbook?.name && item.catalog?.name
        ? `${item.textbook?.name} > ${item.catalog?.name}`
        : "--";
    item.knowledge = item.knowledge?.name;
    // item.content = processContent(item.content);
    item.abilities = item.abilities.map(i => i.abilityDict.name).join(",");
  });
  return obj;
};
const submitLoading = ref(false);
// 保存
const submitForm = async val => {
  if (submitLoading.value) return;
  submitLoading.value = true;
  await saveApi(val);
  submitLoading.value = false;
};
const submitBackLoading = ref(false);
// 保存并返回
const submitFormBack = async val => {
  if (submitBackLoading.value) return;
  submitBackLoading.value = true;
  try {
    await saveApi(val);
    router.go(-1);
  } catch (err) {}
  submitBackLoading.value = false;
};

// 保存api(编辑)
const saveApi = async (val, obj) => {
  console.log("🦄-----val-----", val);
  // const params = {
  //   coursePeriodId: Number(route.query.periodId),
  //   knowledgePoints: []
  // };
  // if (tableData.value.length === 0) {
  //   ElMessage({
  //     type: "error",
  //     message: "请添加知识点内容"
  //   });
  //   return;
  // }
  tableList.value.knowledgePoints = [];
  tableList.value.draftId =
    Number(route.query.draftId) || Number(useCourseStore.draftId);

  tableList.value.coursePeriodId = Number(route.query.periodId)
    ? Number(route.query.periodId)
    : tableList.value.draftId;
  tableData.value.map(item => {
    if (item.uuid) {
      tableList.value.knowledgePoints.push({
        content: item.content,
        subjectId: item.subjectId,
        stageId: item.stageId,
        textbookId: item?.switch1[1],
        catalogId: item?.association.slice(-1)[0],
        knowledgeId: item?.knowledge1.slice(-1)[0],
        abilities: item.abilities1.map(i => {
          return { id: i[1], score: 1 };
        })
      });
    }
  });
  // 判断是草稿箱还是课程
  if (route.query.type === "edite") {
    // console.log(tableList.value.knowledgePoints);
    if (tableList.value.knowledgePoints.length == 0) {
      ElMessage.success("当前资料已保存");
      return;
    }
    tableList.value.coursePeriodId = Number(route.query.periodId);
    const [err, res] = await to(
      saveDraftCourseKnowledge(tableList.value, operateLog)
    );
    if (res.code === 200) {
      ElMessage.success("当前资料已保存");
      getIntroductionfindById();
    } else {
      ElMessage.error("保存失败", res.msg);
    }
  } else {
    const [err, res] = await to(
      saveDraftCourseKnowledgePointNext(tableList.value, operateLog)
    );
    if (res.code === 200) {
      ElMessage.success("当前资料已保存");
      getdraftfindById();
    } else {
      ElMessage.error("保存失败", res.msg);
    }
  }
  // operateLog.value = getLogType(props.infoShow);
  // let api = getApiType(props.infoShow, true);
  // let [err, res] = await requestTo(api(params, operateLog.value));
  // if (res) {
  if (val) {
    if (route.query.fromPage === "courseDetail") {
      router.replace({
        path: "/course/courseDetails",
        query: { id: route.query.courseId }
      });
    } else if (route.query.fromPage === "currentDetail") {
      router.replace({
        path: "/course/courseDetails/currentDetails",
        query: {
          periodId: route.query.periodId,
          courseId: route.query.courseId
        }
      });
    }
  }
  // ElMessage.success("保存成功");
  // } else {
  //   ElMessage.error("保存失败");

  // }
};
const draftLoading = ref(false);
const saveDraftApi = async () => {
  const params = {
    draftId: Number(route.query.draftId) || Number(useCourseStore.draftId),
    content: valueHtml.value
  };

  let api = getApiDraftType(props.infoShow, true);
  let [err, res] = await to(api(params, operateLog.value));
  if (res.code === 200) {
    ElMessage.success("当前资料已保存到草稿箱");
  } else {
    ElMessage.error("保存失败", res.msg);

    if (err) {
    }
  }
};
// 保存草稿箱
const submitDraftLoading = ref(false);
const submitDraftForm = async val => {
  console.log("🎉-----val-----", val);
  if (draftLoading.value) return;
  draftLoading.value = true;
  if (tableData.value.length <= 0) {
    // console.log('🦄-----tableData.value.length-----', tableData.value.length);
    // ElMessage({
    //   type: "error",
    //   message: "请添加知识点"
    // });
    draftLoading.value = false;
    return;
  } else {
    submitDraftLoading.value = true;
    saveApi(val);
    // ElMessage.success("当前资料已保存到草稿箱");
    submitDraftLoading.value = false;
  }
  // else {
  //   await saveDraftApi();
  // }

  draftLoading.value = false;
};
// 上一步
const lastSubmitForm = val => {
  // if (tableData.value.length <= 0) {
  //   saveDraftApi();
  // }
  let info = {
    infoShow: "",
    draftId: Number(route.query.draftId) || Number(useCourseStore.draftId),
    periodName: props.periodName
  };
  if (props.infoShow === "课期知识点") {
    info.infoShow = "课期介绍";
  }
  emites("baseInfo", info);
};
const nextSubmitLoading = ref(false);
// 下一步
const nextSubmitForm = async val => {
  if (nextSubmitLoading.value) return;
  nextSubmitLoading.value = true;

  let info = {
    infoShow: "",
    draftId: Number(route.query.draftId) || Number(useCourseStore.draftId),
    complete: true,
    periodName: props.periodName
  };
  try {
    if (tableData.value.length === 0) {
      ElMessage({
        type: "error",
        message: "请添加知识点"
      });
      nextSubmitLoading.value = false;
      return;
    } else {
      await saveApi(val);
      if (props.infoShow === "课期知识点") {
        info.infoShow = "实践感悟";
      }
      emites("baseInfo", info);
      nextSubmitLoading.value = false;
    }
  } catch (err) {
    console.error(err);
  }
};
const btnData = ref([
  { id: 1, name: "放弃新建" },
  { id: 2, name: "保存到草稿箱" }
]);
const deleteDraft = async () => {
  let operateLogDraft = {
    operateLogType: "COURSE_MANAGEMENT",
    operateType: route.query.draftId
      ? `删除了草稿id为“${route.query.draftId}”的草稿数据`
      : `删除了草稿id为“${useCourseStore.draftId}”的草稿数据`
  };
  const [err, res] = await to(
    draftDelete(
      { id: Number(route.query.draftId) || Number(useCourseStore.draftId) },
      operateLogDraft
    )
  );
  if (res.code === 200) {
    ElMessage.success("删除成功");
  } else {
    ElMessage.error("删除失败");
  }
};
// 退出
const backEvt = (val, it) => {
  if (val === "exit") {
    let titlt =
      it.id === 1
        ? `请确认是否清除当前编辑和提交的所有资料，并删除当前编辑资料在草稿箱中对应的草稿条目`
        : `请确认是否将当前编辑和提交的所有资料保存到草稿箱，并退出编辑页面`;
    ElMessageBox.confirm(`${titlt}`, `退出并${it.name}`, {
      confirmButtonText: "确定",
      cancelButtonText: "取消"
    }).then(() => {
      if (it.id === 1) {
        deleteDraft();
        if (route.query.type === "draft") {
          router.replace("/course/drafts");
        } else if (route.query.type === "edite") {
          router.replace("/course/courseDetails");
        } else {
          router.go(-1);
        }
      } else if (it.id === 2) {
        saveDraftApi();
        if (route.query.type === "draft") {
          router.replace("/course/drafts");
        } else if (route.query.type === "edite") {
          router.replace("/course/courseDetails");
        } else {
          router.go(-1);
        }
      }
    });
  } else {
    if (route.query.fromPage === "courseDetail") {
      router.replace({
        path: "/course/courseDetails",
        query: { id: route.query.courseId }
      });
    } else if (route.query.fromPage === "currentDetail") {
      router.replace({
        path: "/course/courseDetails/currentDetails",
        query: {
          periodId: route.query.periodId,
          courseId: route.query.courseId
        }
      });
    }
  }
};
watch(
  () => props.infoShow,
  () => {
    if (route.query.type === "edite") {
      getIntroductionfindById();
    } else {
      getdraftfindById();
    }
  },
  { immediate: true },
  { deep: true }
);

const confirmLoading = ref(false);
const { tableData, buttons, total, pageInfo } = useTable();

const tableConfig = [
  {
    label: "学段",
    prop: "stage",
    width: "80"
  },
  {
    label: "学科",
    prop: "subject",
    width: "80"
  },
  {
    label: "教材关联",
    prop: "switch",
    width: "300"
  },
  {
    label: "核心知识点",
    prop: "knowledge",
    width: "200"
  },
  {
    label: "预期目标",
    prop: "content"
    // width: "500"
  },
  {
    label: "提升能力",
    prop: "abilities",
    width: "185",
    render: row => {
      if (!row) return "";
      // 将逗号分隔的能力字符串转换为分行显示
      const abilities = row.split(",").map(ability => ability.trim());
      return abilities.join("\n");
    }
  }
];
const findTab = ref(false);
const initialValues = {
  id: "",
  content: "",
  subjectId: "",
  stageId: "",
  textbook: "",
  switch: [],
  catalog: [],
  knowledge: "",
  abilities: []
};
const values = ref({ ...initialValues });
const itemId = ref(null);
const findVal = ref([]);
buttons.value = [
  {
    // 编辑
    text: "编辑",
    code: "edit",
    props: {
      type: "primary"
    },
    show: computed(() => true),
    onClick: async params => {
      // 强制重新渲染弹窗
      dialogKey.value++;
      itemId.value = params.row;
      if (params.row.uuid) {
        values.value.stageId = params.row.stageId;
        values.value.subjectId = params.row.subjectId;
        await Promise.all([
          // findCourseMaterialVersionList(), // 材料关联 options
          findCourseKnowledgePointByTimeList() // 核心知识点 options
          // findCourseKnowledgePointByCityList() // 能力提升 options
        ]);

        values.value = {
          ...params.row,
          switch: params.row.switch1,
          knowledge: params.row.knowledge1,
          abilities: params.row.abilities1
        };
      } else {
        // 已有数据回显
        let res;
        if (route.query.type === "edite") {
          res = await to(findById2({ id: params.row.id }));
        } else {
          res = await to(
            findByIdDraftCourseKnowledgePoint({ id: params.row.id })
          );
        }

        findVal.value = res[1].data;

        if (res[1].code === 200) {
          values.value.stageId = findVal.value?.stage?.id;
          values.value.subjectId = findVal.value?.subject?.id;
          await Promise.all([
            // findCourseMaterialVersionList(), // 材料关联 options
            findCourseKnowledgePointByTimeList() // 核心知识点 options
            // findCourseKnowledgePointByCityList() // 能力提升 options
          ]);
          values.value.content = findVal.value?.content;

          // 使用新方法构建回显数组

          const echoData = buildEchoDataFromFindVal(findVal.value);

          // 设置材料回显
          if (echoData.switchEcho.length > 0) {
            values.value.switch = echoData.switchEcho;
          } else {
            values.value.switch = [];
          }

          // 教材关联回显
          if (echoData.chapterEcho.length > 0) {
            const chapter = await setAssociationEcho(echoData.chapterEcho);
            if (!chapter) {
              values.value.association = echoData.chapterEcho;
            }
            // values.value.association = echoData.chapterEcho;
          } else {
            values.value.association = [];
          }

          // 设置知识点回显
          if (echoData.knowledgeEcho.length > 0) {
            values.value.knowledge = echoData.knowledgeEcho;
          } else {
            values.value.knowledge = [];
          }

          // 能力提升回显
          if (Array.isArray(findVal.value.abilities)) {
            const abilityPaths = findVal.value.abilities.map(item => {
              const path = findPathByValue(
                columns.value[6].options,
                item.abilityDict?.id || item.id
              );

              return path;
            });
            values.value.abilities = abilityPaths.filter(
              path => path.length > 0
            );
          } else {
            // values.value.abilities = [];
          }
        }
      }

      visible.value = true;
      findTab.value = true;
    }
  },
  {
    // 删除
    text: "删除",
    code: "delete",
    props: {
      type: "danger"
    },
    confirm: {
      title: "确认删除",
      message: "确定要删除此记录吗？"
    },
    onConfirm: async params => {
      if (params.row?.uuid) {
        tableData.value.map((item, index) => {
          if (item.uuid == params.row?.uuid) {
            tableData.value.splice(index, 1);
          }
        });
      } else {
        const operateLog = {
          operateLogType: "COURSE_MANAGEMENT",
          operateType: `删除了“${props.periodName}”课期中的一个知识点`
        };
        if (route.query?.type === "edite") {
          let [err, res] = await to(
            deleteCourseKnowledgePoint2({ id: params.row?.id }, operateLog)
          );
          if (res.code === 200) {
            ElMessage.success("删除成功");
            getIntroductionfindById();
          } else {
            console.log(res.msg);
            ElMessage.error(`删除失败: ${res.msg}`);
            return;
          }
        } else {
          let [err, res] = await to(
            deleteDraftCourseKnowledgePoint({ id: params.row?.id }, operateLog)
          );
          if (res.code === 200) {
            ElMessage.success("删除成功");
            getdraftfindById();
          } else {
            ElMessage.error(`删除失败: ${res.msg}`);
          }
        }
      }
    }
  }
];
function findPathByValue(options, targetId) {
  for (const node of options) {
    if (String(node.value) === String(targetId)) return [node.value];
    if (node.children) {
      const childPath = findPathByValue(node.children, targetId);
      if (childPath.length) return [node.value, ...childPath];
    }
  }
  return [];
}

// 新增：调试函数，用于检查知识点数据结构
const debugKnowledgePointStructure = (data, level = 0) => {
  if (!data || !Array.isArray(data)) return;

  data.forEach(item => {
    const indent = "  ".repeat(level);
    console.log(
      `${indent}ID: ${item.id}, Name: ${item.name}, Children: ${item.children?.length || 0}`
    );

    if (item.children && item.children.length > 0) {
      debugKnowledgePointStructure(item.children, level + 1);
    }
  });
};
// 表单操作
const visible = ref(false);
const dialogKey = ref(0);
const addCourseKnowledgePoint = async () => {
  // 重置表单数据
  values.value = deepClone(initialValues);

  // 清空缓存和 options
  materialCache.clear();
  columns.value[2].options = [];
  columns.value[3].options = [];
  columns.value[4].options = [];
  columns.value = [...columns.value];
  // 设置编辑状态
  findTab.value = false;

  // 强制重新渲染弹窗
  dialogKey.value++;

  // 打开弹窗
  visible.value = true;
};

const rules = {
  stageId: [
    {
      required: true,
      message: "请选择学段"
    }
  ],
  subjectId: [
    {
      required: true,
      message: "请选择学科"
    }
  ],
  switch: [
    {
      required: true,
      message: "请选择教材"
    }
  ],
  association: [
    {
      required: true,
      message: "请选择教材关联"
    }
  ],
  knowledge: [
    {
      required: true,
      message: "请选择核心知识点"
    }
  ],
  content: [
    {
      required: true,
      message: "请填写预期目标"
    }
  ],
  abilities: [
    {
      required: true,
      message: "请选择能力提升"
    }
  ]
};
const tableList = ref({
  draftId: 0,
  coursePeriodId: 0,
  knowledgePoints: initialValues
});
const tableList2 = ref({});
// 表单提交
// findTab判断编辑（true）还是新增（false）
const handleSubmit = async () => {
  confirmLoading.value = true;
  tableList.value.knowledgePoints = [];
  const abilities = values.value.abilities.map(item => ({
    id: item[1],
    score: 1
  }));
  const operateLog = {
    operateLogType: "COURSE_MANAGEMENT",
    operateType: `${findTab.value === false ? "添加" : "编辑"}了“${props.periodName}”课期中的课程知识点`
  };
  const list = [...values.value?.switch, ...values.value?.association];
  // const list = [];
  if (findTab.value === false) {
    // console.log(values.value.switch, "values.value.switch");
    try {
      const result = await getMaterialDisplayTextByIds(list);

      tableData.value.push({
        ...values.value,
        stage: columns.value[0].options.find(
          item => item.value === values.value.stageId
        )?.label,
        subject: columns.value[1].options.find(
          item => item.value === values.value.subjectId
        )?.label,
        switch: result,
        switch1: [...values.value.switch],
        knowledge: getDisplayTextByIds(
          columns.value[4].options,
          values.value.knowledge
        ),
        knowledge1: [...values.value.knowledge],
        abilities: getAbilitiesNames(
          values.value.abilities,
          columns.value[6].options
        ),
        abilities1: [...values.value.abilities],
        uuid: uuidv4()
      });
      console.log(values.value.abilities);
    } catch (error) {
      console.error("保存失败:", error);
    }

    console.log("🍪-----tableData.value-----", tableData.value);
  } else {
    if (itemId.value.id) {
      tableList2.value = {
        id: itemId.value.id,
        content: values.value.content,
        subjectId: values.value.subjectId,
        stageId: values.value.stageId,
        textbookId: values.value?.switch[values.value?.switch.length - 1],
        catalogId:
          values.value?.association[values.value?.association.length - 1],
        knowledgeId:
          values.value?.knowledge[values.value?.knowledge.length - 1],
        abilities: [...abilities]
      };
      if (route.query.type === "edite") {
        let [err, res] = await to(
          nowledgeCreateOrUpdate2(tableList2.value, operateLog)
        );
        if (res.code === 200) {
          ElMessage.success("当前资料已保存");
          getIntroductionfindById();
        }
      } else {
        let api = getApiDraftType(props.infoShow, true, operateLog);
        let [err, res] = await to(api(tableList2.value));
        if (res.code === 200) {
          ElMessage.success("当前资料已保存");
          getdraftfindById();
        }
      }
    } else {
      try {
        // 等待 Promise 解决并获取结果
        const result = await getMaterialDisplayTextByIds(list);
        tableData.value.map(item => {
          if (item.uuid === itemId.value.uuid) {
            item.content = values.value.content;
            item.subjectId = values.value.subjectId;
            item.stage = columns.value[0].options.find(
              items => items.value === values.value.stageId
            )?.label;
            item.subject = columns.value[1].options.find(
              items => items.value === values.value.subjectId
            ).label;
            item.switch = result;
            item.switch1 = [...values.value.switch];
            item.knowledge = getDisplayTextByIds(
              columns.value[4].options,
              values.value.knowledge
            );
            item.knowledge1 = [...values.value.knowledge];
            item.abilities = getAbilitiesNames(
              values.value.abilities,
              columns.value[6].options
            );
            item.abilities1 = [...values.value.abilities];
          }
        });
      } catch (error) {
        console.error("编辑失败:", error);
      }
    }
  }

  // setTimeout(() => {
  confirmLoading.value = false;
  visible.value = false;
};

// 能力提升回显
const getAbilitiesNames = (abilitiesArray, options) => {
  if (!Array.isArray(abilitiesArray) || abilitiesArray.length === 0) {
    return "";
  }

  return abilitiesArray
    .map(abilityIds => getAbilityNameByIds(abilityIds, options))
    .filter(name => name) // 过滤掉空值
    .join(",");
};
const getAbilityNameByIds = (abilityIds, options) => {
  if (!Array.isArray(abilityIds) || abilityIds.length === 0) {
    return "";
  }

  // 获取最深层级的能力ID
  const lastAbilityId = abilityIds[abilityIds.length - 1];

  // 递归查找最深层级的能力名称
  const findAbilityName = (options, targetId) => {
    for (const option of options) {
      if (option.value === targetId) {
        return option.label;
      }
      if (option.children && option.children.length > 0) {
        const found = findAbilityName(option.children, targetId);
        if (found) {
          return found;
        }
      }
    }
    return null;
  };

  // 查找并返回能力名称
  const abilityName = findAbilityName(options, lastAbilityId);
  return abilityName || `能力${lastAbilityId}`;
};

// 表单点击
// const handleClick = () => {};
const cascaderRef = ref(null);
watch(
  [() => values.value.stageId, () => values.value.subjectId],
  async ([newStageId, newSubjectId], [oldStageId, oldSubjectId]) => {
    // console.log(oldStageId, oldSubjectId);
    // 当学段和学科都有值时就触发
    if (newStageId && newSubjectId) {
      // 1. 加载核心知识点
      await findCourseKnowledgePointByTimeList();
      await findTextbookSubject();
    }
    if (oldStageId === newStageId || oldSubjectId === newSubjectId) {
      values.value.switch = [];
    }
  },
  { immediate: false }
);

const findListTree = async () => {
  if (cascaderRef.value?.$el) {
    const cascaderEl = cascaderRef.value.$el;

    if (cascaderEl && cascaderEl.dispatchEvent) {
      const clickEvent = new Event("click", { bubbles: true });
      cascaderEl.dispatchEvent(clickEvent);
      console.log("通过 dispatchEvent 触发成功");
      return;
    }
  }
};

const columns = ref([
  {
    label: "学段",
    prop: "stageId",
    valueType: "select",
    options: []
  },
  {
    label: "学科",
    prop: "subjectId",
    valueType: "select",
    options: []
  },
  {
    label: "教材",
    prop: "switch",
    valueType: "cascader",
    options: []
  },
  {
    label: "教材关联",
    prop: "association",
    valueType: "cascader",
    options: []
  },
  {
    label: "核心知识点",
    prop: "knowledge",
    valueType: "cascader",
    options: [],
    props: {
      checkStrictly: false,
      emitPath: true,
      multiple: false,
      filterable: true,
      clearable: true
    }
  },
  {
    label: "预期目标",
    prop: "content",
    valueType: "textarea"
  },
  {
    label: "能力提升",
    prop: "abilities",
    valueType: "cascader",
    options: [],
    props: {
      multiple: true,
      checkStrictly: true
      // filterable: true,
      // clearable: true
    }
  }
]);
// 学段查询
const findCourseKnowledgePointByGradeList = async () => {
  const [err, res] = await to(findCourseKnowledgePointByGrade());
  if (!res.data) return;
  columns.value[0].options = res.data.map(item => {
    return {
      label: item.name,
      value: item.id
    };
  });
};
// 学科查询
const findCourseKnowledgePointBySubjectList = async () => {
  const [err, res] = await to(findCourseKnowledgePointBySubject());
  if (!res.data) return;
  columns.value[1].options = res.data.map(item => {
    return {
      label: item.name,
      value: item.id
    };
  });
};

// 关联材料懒加载 - 使用新的实现
// const CascaderProps = {
//   lazy: true,
//   value: "id",
//   label: "name",
//   children: "children",
//   leaf: "leaf",
//   filterable: true, // 启用搜索功能
//   clearable: true, // 启用清除功能
//   async lazyLoad(node, resolve) {
//     const { level, data } = node;

//     try {
//       // 检查学段和学科是否已选择
//       if (!values.value.stageId || !values.value.subjectId) {
//         console.warn("学段或学科未选择，无法加载材料关联数据");
//         resolve([]);
//         return;
//       }

//       if (level === 0) {
//         // 第一级：加载版本数据
//         const versions = await fetchVersionsData(
//           values.value.stageId,
//           values.value.subjectId
//         );
//         const nodes = versions.map(version => ({
//           id: version.id,
//           name: version.name,
//           leaf: false,
//           children: null,
//           textbooks: version.textbooks,
//           nodeType: "version"
//         }));
//         resolve(nodes);
//       } else if (level === 1) {
//         // 第二级：显示教材数据
//         const textbooks = data.textbooks || [];
//         const nodes = textbooks.map(textbook => ({
//           id: textbook.id,
//           name: textbook.volume, // 使用 volume 作为显示名称
//           leaf: false,
//           children: null,
//           textbookId: textbook.id,
//           nodeType: "textbook"
//         }));
//         resolve(nodes);
//       } else if (level === 2) {
//         // 第三级：根据textbookId加载章节数据
//         const textbookId = data.textbookId;
//         const chapters = await fetchChapterData(textbookId);
//         const nodes = transformChapterData(chapters);
//         resolve(nodes);
//       } else {
//         // 第四级及以上：动态显示章节的子节点
//         const chapterData = data.chapterData;
//         if (
//           chapterData &&
//           chapterData.children &&
//           chapterData.children.length > 0
//         ) {
//           const nodes = transformChapterData(chapterData.children);
//           resolve(nodes);
//         } else {
//           resolve([]);
//         }
//       }
//     } catch (error) {
//       console.error("数据加载失败:", error);
//       resolve([]);
//     }
//   }
// };
// 教材查询
const findTextbookSubject = async () => {
  if (!values.value.stageId || !values.value.subjectId) return;
  const [err, res] = await to(
    findCourseMaterialVersion({
      stageId: values.value.stageId,
      subjectId: values.value.subjectId
    })
  );
  if (res.data) {
    columns.value[2].options = selectList(res.data);
  }
};
// 教材变化调用接口
watch(
  () => values.value.switch,
  async (newVal, oldVal) => {
    if (!newVal) return;
    findMaterialVersion();
  },
  { deep: true, immediate: false }
);
// 教材关联查询
const findMaterialVersion = async () => {
  if (values.value.switch.length === 0) return;
  const [err, res] = await to(
    findCourseDataCourse({
      textbookId: values.value.switch[values.value.switch.length - 1]
    })
  );
  if (res.data) {
    columns.value[3].options = selectList(res.data);
  }
};
const selectList = val => {
  if (!val) return;
  let obj = [];
  val.map(item => {
    obj.push({
      label: item.volume ? item.volume : item.name,
      value: item.id,
      // parentId: item?.parentId || "",
      children:
        selectList(item.textbooks ? item.textbooks : item.children) || []
    });
  });
  return obj;
};
// 核心知识点查询
const findCourseKnowledgePointByTimeList = async () => {
  if (!values.value.stageId || !values.value.subjectId) return;
  try {
    const [err1, res1] = await to(
      findCourseKnowledgePoint({
        stageId: values.value.stageId,
        subjectId: values.value.subjectId
      })
    );

    // console.log("🐠-----res1-----", err1, res1);
    if (!res1.data) return;

    // 使用修复后的 selectList 函数
    const newOptions = selectList(res1.data);
    columns.value[4].options = newOptions;

    // console.log("知识点选项加载完成:", newOptions);
  } catch (error) {
    console.error("加载知识点选项失败:", error);
  }
};
// 能力提升
const findCourseKnowledgePointByCityList = async () => {
  const [err, res] = await to(
    findCourseDataCourseBySubject({
      parentId: 417
    })
  );
  // console.log("🦄-----[err, res-----", err, res);

  if (!res.data) return;
  columns.value[6].options = selectList(res.data);
};
// 表单取消
const handleCancel = async () => {
  // values.value = deepClone(initialValues);

  // 清空缓存
  materialCache.clear();

  // 重置 options
  // columns.value[2].options = [];
  // columns.value[3].options = [];
  // columns.value[5].options = [];
  // columns.value = [...columns.value];
};

/**
 * 根据目标ID在级联选项中查找完整路径
 * @param {Array} options - 级联选项数组
 * @param {string|number} targetId - 目标ID
 * @param {Array} currentPath - 当前路径（递归使用）
 * @returns {Array} 完整路径数组
 */
// const findCascaderPath = (options, targetId, currentPath = []) => {
//   for (const option of options) {
//     const newPath = [...currentPath, option.value];

//     // 找到目标节点
//     if (String(option.value) === String(targetId)) {
//       return newPath;
//     }

//     // 递归查找子节点
//     if (option.children && option.children.length > 0) {
//       const childPath = findCascaderPath(option.children, targetId, newPath);
//       if (childPath.length > 0) {
//         return childPath;
//       }
//     }
//   }

//   return [];
// };

/**
 * 根据ID数组获取显示文本
 * @param {Array} options - 级联选项数组
 * @param {Array} idArray - ID数组
 * @returns {string} 显示文本
 */
const getDisplayTextByIds = (options, idArray) => {
  if (!Array.isArray(idArray) || idArray.length === 0) return "";

  const labels = [];

  const findLabels = (nodes, targetIds) => {
    for (const node of nodes) {
      if (targetIds.includes(String(node.value))) {
        labels.push(node.label);
      }

      if (node.children && node.children.length > 0) {
        findLabels(node.children, targetIds);
      }
    }
  };

  findLabels(
    options,
    idArray.map(id => String(id))
  );
  return labels.join(" > ");
};

// 新增：根据ID获取材料关联显示文本的函数
const getMaterialDisplayTextByIds = async idArray => {
  if (!Array.isArray(idArray) || idArray.length === 0) {
    return "";
  }

  try {
    // 获取版本数据
    const versions = await fetchVersionsData(
      values.value.stageId,
      values.value.subjectId
    );
    const versionId = idArray[0];
    const textbookId = idArray[1];

    const targetVersion = versions.find(v => v.id === versionId);
    if (!targetVersion) {
      return `版本${versionId}`;
    }

    const targetTextbook = targetVersion.textbooks?.find(
      t => t.id === textbookId
    );
    if (!targetTextbook) {
      return `${targetVersion.name} > 教材${textbookId}`;
    }

    let displayText = `${targetVersion.name} > ${targetTextbook.volume}`;

    // 如果有章节ID，获取章节信息
    if (idArray.length > 2) {
      const chapters = await fetchChapterData(textbookId);
      const chapterNames = [];

      for (let i = 2; i < idArray.length; i++) {
        const chapterId = idArray[i];
        const chapter = findChapterById(chapters, chapterId);
        if (chapter) {
          chapterNames.push(chapter.name);
        } else {
          chapterNames.push(`章节${chapterId}`);
        }
      }

      if (chapterNames.length > 0) {
        displayText += ` > ${chapterNames.join(" > ")}`;
      }
    }

    return displayText;
  } catch (error) {
    console.error("获取材料关联显示文本失败:", error);
    return idArray.join(" > ");
  }
};

// 辅助函数：在章节树中查找指定ID的章节
const findChapterById = (chapters, targetId) => {
  for (const chapter of chapters) {
    if (chapter.id === targetId) {
      return chapter;
    }
    if (chapter.children && chapter.children.length > 0) {
      const found = findChapterById(chapter.children, targetId);
      if (found) {
        return found;
      }
    }
  }
  return null;
};

// 递归查找知识点路径
const findKnowledgePathRecursive = (nodes, targetId, currentPath = []) => {
  for (const node of nodes) {
    const newPath = [...currentPath, node.id];

    if (node.id === targetId) {
      return newPath;
    }

    if (node.children && node.children.length > 0) {
      const childPath = findKnowledgePathRecursive(
        node.children,
        targetId,
        newPath
      );
      if (childPath.length > 0) {
        return childPath;
      }
    }
  }
  return [];
};

/**
 * 从findVal.value数据中构建回显数组
 * @param {Object} data findVal.value数据对象
 * @returns {Object} 包含材料关联和知识点回显数组的对象
 */
const buildEchoDataFromFindVal = data => {
  if (!data) {
    console.warn("findVal数据为空");
    return { switchEcho: [], knowledgeEcho: [], chapterEcho: [] };
  }

  try {
    // 构建材料关联回显数组
    const switchEcho = [];
    const chapterEcho = [];

    // 1. 版本ID (来自 textbook.versionId)
    if (data.textbook?.versionId) {
      switchEcho.push(data.textbook.versionId);
    }

    // 2. 教材ID (来自 textbook.id)
    if (data.textbook?.id) {
      switchEcho.push(data.textbook.id);
    }

    // 3. 章节路径 (来自 catalog.parentIds 和 catalog.id)
    if (data.catalog) {
      // 添加父级章节IDs
      if (Array.isArray(data.catalog.parentIds)) {
        chapterEcho.push(...data.catalog.parentIds);
      }
      // 添加当前章节ID
      if (data.catalog.id) {
        chapterEcho.push(data.catalog.id);
      }
    }

    // 构建知识点回显数组
    const knowledgeEcho = [];

    if (data.knowledge) {
      // 添加知识点父级IDs
      if (Array.isArray(data.knowledge.parentIds)) {
        knowledgeEcho.push(...data.knowledge.parentIds);
      }
      // 添加当前知识点ID
      if (data.knowledge.id) {
        knowledgeEcho.push(data.knowledge.id);
      }
    }

    // 构建知识点回显数组 - 修复多层级问题
    // const knowledgeEcho = buildKnowledgeEchoArray(data.knowledge);

    // console.log("构建回显数据成功:", {
    //   switchEcho,
    //   knowledgeEcho,
    //   originalData: data
    // });

    return {
      switchEcho,
      knowledgeEcho,
      chapterEcho
    };
  } catch (error) {
    console.error("构建回显数据失败:", error);
    return { switchEcho: [], knowledgeEcho: [], chapter: [] };
  }
};

// 材料关联回显功能
/**
 * 设置材料关联的回显数据
 * @param {Array} echoIds ID数组，格式为 [版本ID, 教材ID, 章节ID1, 章节ID2, ...]
 * @returns {Promise<boolean>} 回显是否成功
 */
// const setSwitchEchoData = async echoIds => {
//   // console.log('🦄-----echoIds-----', echoIds);
//   if (echoIds.length === 0) {
//     console.warn("关联教材格式不正确");
//     return false;
//   }

//   try {
//     // 验证数据有效性
//     const versionId = echoIds[0];
//     const textbookId = echoIds[1];

//     // 获取版本数据，验证版本ID和教材ID是否存在
//     const versions = await fetchVersionsData();
//     // console.log("🌈-----values.value-----", values.value);
//     const targetVersion = versions.find(v => v.id === versionId);
//     // console.log("🌈-----versions-----", versions);

//     if (!targetVersion) {
//       // console.error(`版本ID ${versionId} 不存在`);
//       return false;
//     }

//     const targetTextbook = targetVersion.textbooks?.find(
//       t => t.id === textbookId
//     );
//     if (!targetTextbook) {
//       console.error(`教材ID ${textbookId} 在版本 ${versionId} 中不存在`);
//       return false;
//     }

//     // 如果有章节数据，验证章节路径
//     if (echoIds.length > 2) {
//       const chapterIds = echoIds.slice(2);
//       await validateSwitchChapterPath(textbookId, chapterIds);
//     }

//     // 设置回显值
//     values.value.switch = echoIds;
//     // console.log("材料关联回显成功:", echoIds);
//     return true;
//   } catch (error) {
//     console.error("材料关联回显失败:", error);
//     return false;
//   }
// };

// 递归验证章节路径是否有效
// const validateSwitchChapterPath = async (textbookId, chapterIds) => {
//   if (chapterIds.length === 0) return;

//   // 获取顶级章节数据
//   const chapters = await fetchChapterData(textbookId);

//   // 递归验证章节路径
//   // await validateSwitchChapterRecursive(chapters, chapterIds, 0);
// };

// 递归验证章节ID路径
// const validateSwitchChapterRecursive = async (
//   chapters,
//   chapterIds,
//   currentIndex
// ) => {
//   console.log(chapterIds, currentIndex, "------------");
//   if (currentIndex >= chapterIds.length) return;

//   const currentChapterId = chapterIds[currentIndex];
//   const currentChapter = chapters.find(c => c.id === currentChapterId);

//   if (!currentChapter) {
//     throw new Error(`章节ID ${currentChapterId} 不存在于当前层级中`);
//   }

//   // // 如果是最后一个ID，验证完成
//   // if (currentIndex === chapterIds.length - 1) {
//   //   // console.log(`章节路径验证完成，最终章节: ${currentChapter.name}`);
//   //   return;
//   // }

//   // 如果还有下一级ID，但当前章节没有子章节，说明路径无效
//   if (!currentChapter.children || currentChapter.children.length === 0) {
//     throw new Error(
//       `章节 ${currentChapter.name} 没有子章节，但路径中还有更多ID`
//     );
//   }

//   // 递归验证下一级
//   await validateSwitchChapterRecursive(
//     currentChapter.children,
//     chapterIds,
//     currentIndex + 1
//   );
// };

// 教材关联回显 - 支持多级
const setAssociationEcho = async chapterIds => {
  // 确保已经选择了教材
  if (!values.value.switch || values.value.switch.length < 2) {
    console.warn("请先选择教材");
    return false;
  }

  try {
    // 先加载教材关联数据
    await findMaterialVersion();

    // 等待DOM更新
    await nextTick();

    // 设置教材关联回显值
    values.value.association = chapterIds;
    // console.log("教材关联多级回显成功:", chapterIds);
    return true;
  } catch (error) {
    console.error("教材关联回显失败:", error);
    return false;
  }
};

const submitDraftFormatt = async targetTab => {
  const tre = ref(false);
  const tbbArr = ["基础信息", "课期行程", "课期介绍"];

  if (tbbArr.includes(targetTab)) {
    submitDraftForm();
    tre.value = true;
    return tre.value;
  }
  if (props.infoShow === "课期知识点") {
    if (tableData.value.length === 0) {
      ElMessage.error("请先添加课期知识点");
      tre.value = false;
      return tre.value;
    } else {
      submitDraftForm();
      tre.value = true;
      return tre.value;
    }
  }

  tre.value = true;
  return tre.value;
};

// 暴露回显方法供外部调用
defineExpose({
  // submitDraftForm,
  submitDraftFormatt,
  buildEchoDataFromFindVal
});
</script>

<template>
  <div class="introduction-edite">
    <div class="content_box">
      <el-button type="primary" @click="addCourseKnowledgePoint">
        添加课期知识点
      </el-button>
      <div class="content">
        <PlusTable
          :columns="tableConfig"
          :table-data="tableData"
          :action-bar="{
            buttons,
            align: 'center'
            // fit: false // 自动适应容器宽度
          }"
          :titleBar="false"
        />
      </div>
    </div>
    <div class="buttons">
      <div v-if="route.query.type === 'edite'" class="left">
        <el-button class="cancel" @click="backEvt('back')"> 返回 </el-button>
        <el-button
          type="primary"
          class="create"
          :loading="submitBackLoading"
          @click="submitFormBack(true)"
        >
          {{ "保存并返回" }}
        </el-button>
        <el-button
          type="primary"
          class="create"
          :loading="submitLoading"
          @click="submitForm(false)"
        >
          {{ "保存" }}
        </el-button>
      </div>
      <div v-else class="left">
        <!-- <el-button class="cancel" @click="backEvt('exit')"> 退出 </el-button> -->
        <el-dropdown>
          <el-button style="margin-right: 10px">退出</el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item
                v-for="it in btnData"
                :key="it.id"
                @click="backEvt('exit', it)"
              >
                {{ it.name }}
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
        <el-button
          type="primary"
          class="create"
          :loading="draftLoading"
          @click="submitDraftForm(ruleFormRef)"
        >
          {{ "保存草稿箱" }}
        </el-button>
        <el-button
          type="primary"
          class="create"
          :loading="submitLoading"
          @click="lastSubmitForm(ruleFormRef)"
        >
          {{ "上一步" }}
        </el-button>
        <el-button
          type="primary"
          class="create"
          :loading="nextSubmitLoading"
          @click="nextSubmitForm(ruleFormRef)"
        >
          {{ "下一步" }}
        </el-button>
      </div>
      <div class="right">
        <el-button
          type="primary"
          class="create"
          @click="aiNewPage(infoShowEnName)"
        >
          {{ "AI课程设计" }}
        </el-button>
      </div>
    </div>
    <!-- 表单弹窗 -->
    <PlusDialogForm
      :key="dialogKey"
      v-model:visible="visible"
      v-model="values"
      :form="{ columns, rules }"
      :dialog="{ title: '课期知识点', confirmLoading }"
      @confirm="handleSubmit()"
      @close="handleCancel"
    >
      <!-- 学段选择器 -->
      <template #plus-field-stageId="{ options }">
        <el-select
          v-model="values.stageId"
          filterable
          placeholder="请选择学段"
          style="width: 100%"
        >
          <el-option
            v-for="item in options"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </template>

      <!-- 学科选择器 -->
      <template #plus-field-subjectId="{ options }">
        <el-select
          v-model="values.subjectId"
          filterable
          placeholder="请选择学段"
          style="width: 100%"
        >
          <el-option
            v-for="item in options"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </template>

      <!-- 预期目标文本框 -->
      <template #plus-field-content>
        <el-input
          v-model="values.content"
          type="textarea"
          :rows="6"
          :maxlength="300"
          :show-word-limit="true"
          placeholder="请输入预期目标（最多300字）"
          style="width: 100%"
        />
      </template>

      <template #plus-field-abilities="{ options }">
        <el-cascader
          v-model="values.abilities"
          :options="options"
          :props="{
            multiple: true
          }"
          :show-all-levels="false"
          filterable
          clearable
        />
      </template>
      <template #plus-field-knowledge="{ options }">
        <div v-if="values.stageId && values.subjectId">
          <el-cascader
            v-model="values.knowledge"
            :options="options"
            filterable
            clearable
          />
        </div>
        <div v-else class="field-disabled">
          <el-input
            placeholder="请先选择学段和学科"
            disabled
            style="width: 100%"
          />
        </div>
      </template>
      <template #plus-field-switch="{ options }">
        <div v-if="values.stageId && values.subjectId">
          <el-cascader
            ref="cascaderRef"
            v-model="values.switch"
            :options="options"
            clearable
            filterable
            placeholder="请选择教材"
            style="width: 100%"
          />
        </div>
        <div v-else class="field-disabled">
          <el-input
            placeholder="请先选择学段和学科"
            disabled
            style="width: 100%"
          />
        </div>
      </template>
      <template #plus-field-association="{ options }">
        <div v-if="values.switch?.length > 0">
          <el-cascader
            ref="cascaderRef"
            v-model="values.association"
            :options="options"
            clearable
            filterable
            placeholder="请选择教材关联"
            style="width: 100%"
          />
        </div>
        <div v-else class="field-disabled">
          <el-input placeholder="请先选择教材" disabled style="width: 100%" />
        </div>
      </template>
    </PlusDialogForm>
  </div>
</template>

<style lang="scss" scoped>
.introduction-edite {
  box-sizing: border-box;
  width: 100%;
  // width: 93%;
  height: 100%;
  position: relative;
  padding: 10px 0 0 0;
  background-color: #fff;
  // background-color: #8a1111;
  // display: flex;
  .content_box {
    // width: calc(100% - 5%);
    // background-color: red;
    // width: 100%;
    // height: calc(100% - 40px);
    overflow-y: auto;
  }
  .buttons {
    display: flex;
    width: 100%;
    position: absolute;
    bottom: 0;
    left: 0;
    justify-content: space-between;
    // padding-right: 80px;
    background: #fff;
    z-index: 20;
    border-top: 1px solid #f0f0f0;
  }
}
.content {
  padding: 15px 0;
  min-width: 50%;
}
:deep(.w-e-text-placeholder) {
  top: 6px;
  left: 14px;
}
:deep(.el-button:focus-visible) {
  display: none;
}
:deep(.el-form-item__label) {
  width: 100px !important;
  position: relative;
  display: flex;
  justify-content: right;
}
:deep(.el-input__wrapper) {
  width: 667px;
}
:deep(.el-link__inner) {
  margin: 5px;
}

:deep(.el-link::after) {
  border: none !important;
}

// 添加禁用字段的样式
.field-disabled {
  .el-input.is-disabled .el-input__wrapper {
    background-color: #f5f7fa;
    border-color: #e4e7ed;
    color: #c0c4cc;
  }
}

// 添加提升能力列的换行样式
:deep(.el-table .cell) {
  white-space: pre-line;
}
</style>
