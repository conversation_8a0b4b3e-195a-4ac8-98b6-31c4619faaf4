<script setup>
import { onMounted, ref } from "vue";
import { formatTime } from "@/utils/index";
import dayjs from "dayjs";
import {
  CoursePeriodIdByFree,
  findSpecificationByCoursePeriodId
} from "@/api/period.js";
import { requestTo } from "@/utils/http/tool";
import { ElMessage, ElMessageBox } from "element-plus";
import { useRouter, useRoute } from "vue-router";
import { courseStore } from "@/store/modules/course.js";
import { to } from "@iceywu/utils";
import {
  priceSettingFindByDraftId,
  findSpecificationTableByDraftId,
  findFeeItemByDraftId
} from "@/api/drafts.js";
import { findCouponPageByCoursePeriodId } from "@/api/coupon.js";

const props = defineProps({
  draftId: {
    type: String,
    default: ""
  },
  height: {
    type: String,
    default: "100%"
  },
  coursePeriodId: {
    type: String,
    default: ""
  },
  width: {
    type: String,
    default: "100%"
  },
  coursePeriodDetails: {
    type: Object,
    default: () => ({})
  }
});
const router = useRouter();
const route = useRoute();

onMounted(() => {
  if (props.draftId === "") {
    getTableList();
    getFreeData();
    getCouponList();
  } else {
    // getTableListVt();
    getFreeDataVt();
    getCouponList();
  }
  // tableData.value = Array.from({ length: 20 }).fill({
  //   completed_at: "0",
  //   pending_review: 1,
  //   createdAt: 172627952334,
  //   name: "2024-09-14习题任务",
  //   termNumber: 1,
  //   courseTypeName: "手工",
  //   state: 1,
  //   organizationName: "2024-09-14习题任务",
  //   freeze: 1
  // });
});
// 表格数据
const tableData = ref([]);
const tableTitle = ref([]);
// 退款和费用数据
const freeInfo = ref({
  freeValue: "",
  refundValue: "",
  couponAvailable: false
});

// 新的价格设置数据
const priceSettings = ref({
  coursePrice: "", // 课程费用
  materialPrice: "", // 材料费用
  priceType: "fixed", // 价格类型：fixed(是) 或 negotiable(否)
  priceDescription: "", // 费用说明
  // 促销价相关字段
  promotionEnabled: false, // 是否开启促销价
  promotionPrice: "", // 促销价
  ruleFirstNEnabled: false, // 规则1：前 N 名
  ruleFirstNCount: "", // N 值
  ruleTimeEnabled: false, // 规则2：时间范围
  ruleTimeRange: [] // [开始时间, 结束时间]
});
const params = ref({
  page: 1,
  size: 10,
  sort: "createdAt,desc",
  totalElements: 0
});

// 优惠券相关数据
const couponData = ref([]);
const couponLoading = ref(false);
const couponPagination = ref({
  total: 0,
  pageSize: 5,
  currentPage: 1,
  background: true,
  pageSizes: [5, 10, 15, 20]
});

// 优惠券表格列定义
const couponColumns = [
  {
    label: "优惠券名称",
    prop: "name",
    minWidth: 120,
    formatter: ({ name }) => name || "--"
  },
  {
    label: "优惠规则",
    prop: "discountAmount",
    minWidth: 100,
    formatter: ({ discountAmount, couponDiscountType, conditionAmount }) => {
      if (!discountAmount) return "--";
      let rule = "";
      if (couponDiscountType === "FULL_REDUCTION") {
        rule = `满${conditionAmount || 0}减${discountAmount}`;
      } else if (couponDiscountType === "DISCOUNT") {
        rule = `${discountAmount}折`;
      } else if (couponDiscountType === "FIXED") {
        rule = `减${discountAmount}元`;
      }
      return rule;
    }
  },
  {
    label: "优惠费用类型",
    prop: "feeType",
    minWidth: 100,
    formatter: ({ feeType }) => {
      const typeMap = {
        CLASS_HOUR: "课时",
        INSURANCE: "保险",
        MATERIAL: "材料",
        SERVICE: "服务"
      };
      return typeMap[feeType] || "--";
    }
  },
  {
    label: "发放时间",
    prop: "distributionStartTime",
    minWidth: 180,
    formatter: ({ distributionStartTime, distributionEndTime }) => {
      if (!distributionStartTime || !distributionEndTime) return "--";
      const startTime = dayjs(distributionStartTime).format("YYYY-MM-DD");
      const endTime = dayjs(distributionEndTime).format("YYYY-MM-DD");
      return `${startTime}至${endTime}`;
    }
  },
  {
    label: "使用时间",
    prop: "startTime",
    minWidth: 180,
    formatter: ({ startTime, endTime, isUseLimit }) => {
      if (!isUseLimit) return "不限制";
      if (!startTime || !endTime) return "--";
      const start = dayjs(startTime).format("YYYY-MM-DD");
      const end = dayjs(endTime).format("YYYY-MM-DD");
      return `${start}至${end}`;
    }
  }
];
// 获取规格列表信息
const getListLoading = ref(false);
const getTableList = async data => {
  if (getListLoading.value) {
    return;
  }
  getListLoading.value = true;
  let paramsData = {
    coursePeriodId: route.query.periodId
  };
  // console.log("🍧-----paramsData-----", paramsData);
  const [err, result] = await requestTo(
    findSpecificationByCoursePeriodId(paramsData)
  );
  console.log("🎁-----result--22---", result);
  if (result && Array.isArray(result)) {
    // 处理新的数据结构，解析费用项目
    result.forEach(item => {
      if (item.feeType === "CLASS_HOUR") {
        priceSettings.value.coursePrice =
          item.amount !== undefined && item.amount !== null ? item.amount : "";

        // 处理促销价数据回显
        if (item.promotion) {
          // 有促销价数据，设置是否开启促销价为是
          priceSettings.value.promotionEnabled = true;

          // 回显促销价
          priceSettings.value.promotionPrice =
            item.promotion.promotionPrice !== undefined &&
            item.promotion.promotionPrice !== null
              ? item.promotion.promotionPrice
              : "";

          // 回显促销规则：前N名用户
          if (item.promotion.totalQuota) {
            priceSettings.value.ruleFirstNEnabled = true;
            priceSettings.value.ruleFirstNCount = item.promotion.totalQuota;
          }

          // 回显促销规则：时间范围
          if (item.promotion.startTime && item.promotion.endTime) {
            priceSettings.value.ruleTimeEnabled = true;
            // 将时间戳转换为日期时间格式
            const startTime = dayjs(item.promotion.startTime).format(
              "YYYY-MM-DD HH:mm:ss"
            );
            const endTime = dayjs(item.promotion.endTime).format(
              "YYYY-MM-DD HH:mm:ss"
            );
            priceSettings.value.ruleTimeRange = [startTime, endTime];
          }
        } else {
          // 没有促销价数据，重置促销价相关字段
          priceSettings.value.promotionEnabled = false;
          priceSettings.value.promotionPrice = "";
          priceSettings.value.ruleFirstNEnabled = false;
          priceSettings.value.ruleFirstNCount = "";
          priceSettings.value.ruleTimeEnabled = false;
          priceSettings.value.ruleTimeRange = [];
        }
      } else if (item.feeType === "MATERIAL") {
        priceSettings.value.materialPrice =
          item.amount !== undefined && item.amount !== null ? item.amount : "";
        // 材料费用的mandatory决定priceType
        priceSettings.value.priceType = item.mandatory ? "fixed" : "negotiable";
      }
    });

    // 清空表格数据，因为新的数据结构不再需要表格展示
    tableData.value = [];
    tableTitle.value = [];
  } else {
    console.log("没有数据");
  }
  getListLoading.value = false;
};
// 草稿箱获取规格列表信息V2
const getTableListVt = async data => {
  if (getListLoading.value) {
    return;
  }
  getListLoading.value = true;
  let paramsData = {
    draftId: props.draftId
  };
  const [err, result] = await requestTo(
    findSpecificationTableByDraftId(paramsData)
  );
  if (result) {
    console.log("🎁-----result-----", result);
    tableData.value = result?.content.map(item => {
      let obj = {};
      result?.headers.forEach((header, index) => {
        if (header === "价格") {
          obj[header] = item[index] + " 元";
        } else {
          obj[header] = item[index];
        }
      });
      return obj;
    });
    tableTitle.value = result?.headers.map(it => {
      return {
        label: it,
        prop: it
      };
    });
  } else {
    console.log("没有数据");
  }
  getListLoading.value = false;
};

// 获取费用说明及退款信息
const getLoading = ref(false);
const getFreeData = async data => {
  if (getLoading.value) {
    return;
  }
  getLoading.value = true;
  let paramsData = {
    coursePeriodId: route.query.periodId
  };
  const [err, result] = await to(CoursePeriodIdByFree(paramsData));
  if (result?.code === 200) {
    freeInfo.value.freeValue = result?.data?.feeDescription;
    freeInfo.value.refundValue = result?.data?.refundPolicy;
    freeInfo.value.couponAvailable = result?.data?.couponAvailable;
    if (result?.data?.feeDescription) {
      courseStore().saveFreeInfo(result?.data?.feeDescription);
    } else {
      courseStore().saveFreeInfo("");
    }
    if (result?.data?.refundPolicy) {
      courseStore().saveRefundInfo(result?.data?.refundPolicy);
    } else {
      courseStore().saveRefundInfo("");
    }
  } else {
    // ElMessage.error('获取失败');
    console.log("无数据");
  }
  getLoading.value = false;
};
// 获取费用说明及退款信息V2
const getFreeDataVt = async data => {
  if (getLoading.value) {
    return;
  }
  getLoading.value = true;
  let paramsData = {
    draftId: props.draftId
  };
  const [err, result] = await to(priceSettingFindByDraftId(paramsData));
  if (result?.code === 200) {
    freeInfo.value.freeValue = result?.data?.feeDescription;
    freeInfo.value.refundValue = result?.data?.refundPolicy;
    freeInfo.value.couponAvailable = result?.data?.couponAvailable;
    if (result?.data?.feeDescription) {
      courseStore().saveFreeInfo(result?.data?.feeDescription);
    } else {
      courseStore().saveFreeInfo("");
    }
    if (result?.data?.refundPolicy) {
      courseStore().saveRefundInfo(result?.data?.refundPolicy);
    } else {
      courseStore().saveRefundInfo("");
    }
  } else {
    console.log("无数据");
  }

  // 获取费用项目数据
  await getFeeItemData();
  getLoading.value = false;
};

// 获取费用项目数据
const getFeeItemData = async () => {
  if (!props.draftId) return;

  const [err, result] = await requestTo(
    findFeeItemByDraftId({
      draftId: Number(props.draftId)
    })
  );

  if (result && Array.isArray(result)) {
    result.forEach(item => {
      if (item.feeType === "CLASS_HOUR") {
        priceSettings.value.coursePrice =
          item.amount !== undefined && item.amount !== null ? item.amount : "";

        // 处理促销价数据回显
        if (item.promotion) {
          // 有促销价数据，设置是否开启促销价为是
          priceSettings.value.promotionEnabled = true;

          // 回显促销价
          priceSettings.value.promotionPrice =
            item.promotion.promotionPrice !== undefined &&
            item.promotion.promotionPrice !== null
              ? item.promotion.promotionPrice
              : "";

          // 回显促销规则：前N名用户
          if (item.promotion.totalQuota) {
            priceSettings.value.ruleFirstNEnabled = true;
            priceSettings.value.ruleFirstNCount = item.promotion.totalQuota;
          }

          // 回显促销规则：时间范围
          if (item.promotion.startTime && item.promotion.endTime) {
            priceSettings.value.ruleTimeEnabled = true;
            // 将时间戳转换为日期时间格式
            const startTime = dayjs(item.promotion.startTime).format(
              "YYYY-MM-DD HH:mm:ss"
            );
            const endTime = dayjs(item.promotion.endTime).format(
              "YYYY-MM-DD HH:mm:ss"
            );
            priceSettings.value.ruleTimeRange = [startTime, endTime];
          }
        } else {
          // 没有促销价数据，重置促销价相关字段
          priceSettings.value.promotionEnabled = false;
          priceSettings.value.promotionPrice = "";
          priceSettings.value.ruleFirstNEnabled = false;
          priceSettings.value.ruleFirstNCount = "";
          priceSettings.value.ruleTimeEnabled = false;
          priceSettings.value.ruleTimeRange = [];
        }
      } else if (item.feeType === "MATERIAL") {
        priceSettings.value.materialPrice =
          item.amount !== undefined && item.amount !== null ? item.amount : "";
        // 材料费用的mandatory决定priceType
        priceSettings.value.priceType = item.mandatory ? "fixed" : "negotiable";
      }
    });
  }
};

// 获取优惠券列表
const getCouponList = async () => {
  if (couponLoading.value) return;

  couponLoading.value = true;

  // 草稿箱模式下暂不显示优惠券
  if (props.draftId) {
    couponData.value = [];
    couponPagination.value.total = 0;
    couponLoading.value = false;
    return;
  }

  const paramsData = {
    coursePeriodId: route.query.periodId,
    page: couponPagination.value.currentPage - 1,
    size: couponPagination.value.pageSize,
    sort: "createdAt,desc"
  };

  // 如果没有课期ID，则不请求
  if (!paramsData.coursePeriodId) {
    couponLoading.value = false;
    return;
  }

  const [err, result] = await requestTo(
    findCouponPageByCoursePeriodId(paramsData)
  );

  if (result) {
    couponData.value = result.content || [];
    couponPagination.value.total = result.totalElements || 0;
  } else {
    couponData.value = [];
    couponPagination.value.total = 0;
  }

  couponLoading.value = false;
};

// 优惠券分页处理
const handleCouponSizeChange = size => {
  couponPagination.value.pageSize = size;
  couponPagination.value.currentPage = 1;
  getCouponList();
};

const handleCouponCurrentChange = page => {
  couponPagination.value.currentPage = page;
  getCouponList();
};

const text = ref("");
// 编辑价格
const editePrice = () => {
  if (tableTitle.value && tableTitle.value.length > 0) {
    text.value = "value";
  }
  router.push({
    path: "/course/currentDetails/priceEdite",
    query: {
      periodId: route.query.periodId,
      back: text.value
    }
  });
};
// 费用退款
const editeFree = (val, text) => {
  router.push({
    path: "/course/currentDetails/freeEdite",
    query: {
      type: val,
      periodId: route.query.periodId,
      text: text
    }
  });
};
</script>

<template>
  <div class="containers">
    <!-- 规格表格相关代码已注释 -->
    <!-- <div class="con_table">
      <div v-if="tableData?.length" class="table-content">
        <pure-table
          ref="tableRef"
          row-key="id"
          adaptive
          :adaptiveConfig="{ offsetBottom: 108 }"
          table-layout="auto"
          :loading="loadingTable"
          :size="size"
          :data="tableData"
          :columns="tableTitle"
          :style="{ height: '100%' }"
          :header-cell-style="{
            background: 'var(--el-fill-color-light)',
            color: 'var(--el-text-color-primary)'
          }"
          @drag-sort-end="handleSortEnd"
        />
      </div>
      <el-skeleton v-else-if="getListLoading" :rows="5" animated />
      <el-empty v-else description="暂无数据" class="w100% h248px" />

      <el-button
        v-if="false"
        :disabled="getListLoading"
        type="primary"
        class="editeBtn"
        @click="editePrice"
      >
        编辑价格
      </el-button>
    </div> -->

    <!-- 新的价格设置展示 -->
    <div class="price-setting-section">
      <div class="price-description-list">
        <!-- 课程费用 -->
        <div class="description-item">
          <div class="label">课时费用：</div>
          <div class="value">
            {{
              priceSettings.coursePrice !== "" &&
              priceSettings.coursePrice !== null &&
              priceSettings.coursePrice !== undefined
                ? `${priceSettings.coursePrice} 元`
                : "暂无数据"
            }}
          </div>
        </div>

        <!-- 促销价相关展示（仅在开启促销价时显示） -->
        <template v-if="priceSettings.promotionEnabled">
          <!-- 促销价 -->
          <div class="description-item">
            <div class="label">促销价：</div>
            <div class="value">
              {{
                priceSettings.promotionPrice !== "" &&
                priceSettings.promotionPrice !== null &&
                priceSettings.promotionPrice !== undefined
                  ? `${priceSettings.promotionPrice} 元`
                  : "暂无数据"
              }}
            </div>
          </div>

          <!-- 促销规则 -->
          <div class="description-item promotion-rules">
            <div class="label">促销规则：</div>
            <div class="value">
              <div v-if="priceSettings.ruleFirstNEnabled" class="rule-text">
                前{{ priceSettings.ruleFirstNCount }}名用户可以享受促销价
              </div>
              <div v-if="priceSettings.ruleTimeEnabled" class="rule-text">
                {{ priceSettings.ruleTimeRange[0] }} 至
                {{ priceSettings.ruleTimeRange[1] }} 前报名用户可以享受促销价
              </div>
              <div
                v-if="
                  !priceSettings.ruleFirstNEnabled &&
                  !priceSettings.ruleTimeEnabled
                "
                class="rule-text"
              >
                暂无促销规则
              </div>
            </div>
          </div>
        </template>

        <!-- 材料费用 -->
        <div class="description-item">
          <div class="label">材料费用：</div>
          <div class="value">
            {{
              priceSettings.materialPrice !== "" &&
              priceSettings.materialPrice !== null &&
              priceSettings.materialPrice !== undefined
                ? `${priceSettings.materialPrice} 元${priceSettings.priceType === "fixed" ? "（家长必选材料费用）" : "（家长可选材料费用）"}`
                : "暂无数据"
            }}
          </div>
        </div>

        <!-- 是否可以使用优惠券 -->
        <div class="description-item">
          <div class="label">是否可以使用优惠券：</div>
          <div class="value">
            {{
              // 优先使用父组件传递的couponAvailable字段，如果没有则使用本组件获取的数据
              props.coursePeriodDetails?.couponAvailable !== undefined
                ? props.coursePeriodDetails.couponAvailable
                  ? "是"
                  : "否"
                : freeInfo.couponAvailable
                  ? "是"
                  : "否"
            }}
          </div>
        </div>
      </div>
    </div>

    <!-- 可用指定优惠券表格 -->
    <div class="coupon-section">
      <div class="title">
        <div class="text">可用指定优惠券</div>
      </div>
      <div class="coupon-table-container">
        <pure-table
          ref="couponTableRef"
          row-key="id"
          adaptive
          :adaptiveConfig="{ offsetBottom: 108 }"
          table-layout="auto"
          :loading="couponLoading"
          :data="couponData"
          :columns="couponColumns"
          :pagination="{ ...couponPagination }"
          :header-cell-style="{
            background: 'var(--el-fill-color-light)',
            color: 'var(--el-text-color-primary)'
          }"
          @page-size-change="handleCouponSizeChange"
          @page-current-change="handleCouponCurrentChange"
        />
      </div>
    </div>

    <div class="content">
      <div class="free">
        <div class="title">
          <div class="text">费用说明（包含、不包含）</div>
          <el-button
            v-if="false"
            type="primary"
            @click="editeFree('free', '费用说明（包括、不包括）')"
          >
            编辑费用说明
          </el-button>
        </div>
        <div class="text-box">
          <!-- <el-input
            v-model="freeInfo.freeValue"
            :rows="7"
            type="textarea"
            resize="none"
            disabled
            :placeholder="!freeInfo.freeValue ? '暂无数据' : ''"
          /> -->
          {{ freeInfo.freeValue || "暂无数据" }}
        </div>
      </div>
      <div class="refund">
        <div class="title">
          <div class="text">退款政策</div>
          <el-button
            v-if="false"
            type="primary"
            @click="editeFree('refund', '退款政策')"
          >
            编辑退款政策
          </el-button>
        </div>
        <div class="text-box">
          <!-- <el-input
            v-model="freeInfo.refundValue"
            :rows="7"
            type="textarea"
            resize="none"
            disabled
            :placeholder="!freeInfo.refundValue ? '暂无数据' : ''"
          /> -->
          {{ freeInfo.refundValue || "暂无数据" }}
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.containers {
  box-sizing: border-box;
  //   width: calc(100% - 48px);
  display: flex;
  flex-direction: column;
  height: v-bind(height);
  background: #fff;
  overflow-y: auto;
  .text-box {
    border: 1px solid #e5e5e5;
    border-radius: 3px;
    width: 100%;
    height: 200px;
    padding: 5px 10px;
    font-size: 14px;
    overflow-y: auto;
  }
  .con_table {
    // width: calc(100% - 25px);
    margin-bottom: 20px;
    // margin-left: 25px;
    display: flex;
    flex: 1;
    min-height: 0;
    // height: 258px;
    .table-content {
      width: 100%;
      // height: 258px; // 移除
      flex: 1;
      min-height: 0;
      display: flex;
      // :deep(.el-table) {
      //   width: 97%;
      // }
      :deep(.pure-table) {
        // width: 100%;
        width: v-bind(width) !important;
        display: flex;
        flex-direction: column;
        :deep(.el-table__body-wrapper) {
          overflow-y: auto; // 确保表格内容超出时可以滚动
        }
      }
    }
    .el-empty {
      flex: 1;
    }

    .editeBtn {
      margin-top: 10px;
      align-self: flex-start;
    }
  }

  .content {
    box-sizing: border-box;
    display: flex;
    padding: 10px 0 10px 0;
    // overflow-y: auto;

    .free {
      width: 50%;
      margin-right: 40px;
    }

    .title {
      margin-bottom: 20px;
      font-size: 14px;
      color: rgb(16 16 16 / 100%);
      display: flex;
      justify-content: space-between;
    }

    .refund {
      width: 50%;
    }

    .text {
      width: 100%;
    }
  }
}
.containers::-webkit-scrollbar-track {
  background: #f1f1f1; /* 滚动条轨道背景 */
}

.containers::-webkit-scrollbar-thumb {
  background: #afafaf; /* 滚动条滑块颜色 */
}

.containers::-webkit-scrollbar-thumb:hover {
  background: #7c7c7c; /* 滚动条滑块悬停时的颜色 */
}

// 新的价格设置样式
.price-setting-section {
  margin-bottom: 20px;
  background: #fff;
  border-radius: 8px;

  .title {
    font-size: 16px;
    font-weight: 600;
    color: #303133;
    margin-bottom: 20px;
  }

  .price-description-list {
    .description-item {
      display: flex;
      align-items: center;
      margin-bottom: 16px;
      padding: 12px 0;

      &:last-child {
        margin-bottom: 0;
      }

      .label {
        font-size: 14px;
        color: #606266;
        font-weight: 500;
        min-width: 120px;
        flex-shrink: 0;
      }

      .value {
        font-size: 14px;
        color: #303133;
        flex: 1;
      }

      &.promotion-rules {
        align-items: flex-start;

        .value {
          display: flex;
          flex-direction: column;

          .rule-text {
            margin-bottom: 16px;
            font-size: 14px;
            color: #303133;

            &:last-child {
              margin-bottom: 0;
            }
          }
        }
      }
    }
  }
}

// 优惠券表格样式
.coupon-section {
  margin-bottom: 20px;
  background: #fff;
  border-radius: 8px;

  .title {
    margin-bottom: 20px;
    font-size: 14px;
    color: rgb(16 16 16 / 100%);
    display: flex;
    justify-content: space-between;

    .text {
      font-weight: 500;
    }
  }

  .coupon-table-container {
    min-height: 200px;

    :deep(.pure-table) {
      width: 100% !important;
      display: flex;
      flex-direction: column;

      .el-table__body-wrapper {
        overflow-y: auto;
      }
    }
  }
}
</style>
