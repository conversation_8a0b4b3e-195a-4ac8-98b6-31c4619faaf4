<script setup>
import useAddCoupon from "./utils/addCoupon.jsx";
import { PlusSearch } from "plus-pro-components";

// 使用组合式函数
const {
  // 路由相关
  router,
  route,

  // 表单相关
  form,
  formRef,
  formData,
  formData2,
  rules,

  // 使用范围相关
  scopeForm,
  searchColumns,
  tableColumns,
  tableData,
  stableTableData,
  showScopeSelection,

  // 分页相关
  pagination,

  // 状态相关
  loading,
  getListLoading,
  selectedRows,
  selectedRowKeys,
  richFlag,
  formFile,
  rowSelection,

  // 方法
  onSearch,
  onReset,
  onPageChange,
  onSizeChange,
  onSelectionChange,
  onScopeChange,
  reset,
  submitForm,
  onSubmit,
  tableRef,
  handleTotalIssueInput,
  // 其他数据
  newData,
  oldData
} = useAddCoupon();
</script>

<template>
  <div class="coupon-create">
    <el-scrollbar class="scrollbar">
      <div class="common">
        <!-- 基本信息 -->
        <div class="section-title">优惠券基础信息</div>
        <el-form
          ref="formRef"
          :model="form"
          :rules="rules"
          label-width="120px"
          class="coupon-form"
        >
          <div class="form-section">
            <!-- 基本信息表单项 -->
            <el-form-item
              v-for="(item, index) in formData"
              v-show="!item.show || item.show()"
              :key="index"
              :label="item.label"
              :prop="item.prop"
              style="margin-bottom: 30px"
            >
              <!-- input输入 -->
              <template v-if="item.type === 'input'">
                <el-input
                  v-model.trim="form[item.prop]"
                  :placeholder="item.placeholder"
                  :style="{ width: item.width || '400px' }"
                  :maxlength="item.maxLength"
                  :show-word-limit="item.maxLength"
                  :type="item.types"
                  @input="handleTotalIssueInput"
                />
              </template>

              <!-- textarea输入 -->
              <template v-if="item.type === 'textarea'">
                <el-input
                  v-model="form[item.prop]"
                  :rows="4"
                  type="textarea"
                  :style="{ width: item.width || '400px' }"
                  :placeholder="item.placeholder"
                  :maxlength="item.maxLength"
                  :show-word-limit="item.maxLength"
                />
              </template>

              <!-- 单选框 -->
              <template v-if="item.type === 'radio'">
                <el-radio-group v-model="form[item.prop]">
                  <el-radio
                    v-for="it in item.options"
                    :key="it.value"
                    :label="it.value"
                  >
                    {{ it.name }}
                  </el-radio>
                </el-radio-group>
              </template>

              <!-- 日期选择 -->
              <template v-if="item.type === 'date'">
                <!-- 发放时间范围 -->
                <div
                  v-if="item.prop === 'distributionStartTime'"
                  class="time-range"
                >
                  <span>开始</span>
                  <el-date-picker
                    v-model="form.distributionStartTime"
                    placeholder="请选择开始时间"
                    style="width: 200px; margin: 0 10px"
                    type="date"
                    value-format="YYYY-MM-DD"
                    format="YYYY-MM-DD"
                  />
                  <span>结束</span>
                  <el-date-picker
                    v-model="form.distributionEndTime"
                    placeholder="请选择结束时间"
                    style="width: 200px; margin-left: 10px"
                    type="date"
                    value-format="YYYY-MM-DD"
                    format="YYYY-MM-DD"
                  />
                </div>
                <!-- 使用时间范围 -->
                <div v-else-if="item.prop === 'startTime'" class="time-range">
                  <span>开始</span>
                  <el-date-picker
                    v-model="form.startTime"
                    placeholder="请选择开始时间"
                    style="width: 200px; margin: 0 10px"
                    type="date"
                    value-format="YYYY-MM-DD"
                    format="YYYY-MM-DD"
                  />
                  <span>结束</span>
                  <el-date-picker
                    v-model="form.endTime"
                    placeholder="请选择结束时间"
                    style="width: 200px; margin-left: 10px"
                    type="date"
                    value-format="YYYY-MM-DD"
                    format="YYYY-MM-DD"
                  />
                </div>
              </template>

              <!-- 满减金额输入 -->
              <template v-if="item.type === 'radioInput'">
                <div class="discount-input-group">
                  <span class="form-text">满</span>
                  <el-form-item
                    prop="conditionAmount"
                    style="display: inline-block; width: 180px"
                  >
                    <el-input
                      v-model="form.conditionAmount"
                      placeholder="请输入满减条件金额"
                      style="width: 170px"
                      type="number"
                      @change="handleConditionAmountChange"
                    />
                  </el-form-item>
                  <span class="form-text">减</span>
                  <el-form-item
                    prop="discountAmount"
                    style="display: inline-block; width: 180px"
                  >
                    <el-input
                      v-model="form.discountAmount"
                      placeholder="请输入优惠金额"
                      style="width: 170px"
                      type="number"
                      @change="handleDiscountAmountChange"
                    />
                  </el-form-item>
                </div>
              </template>
            </el-form-item>
          </div>
        </el-form>

        <!-- 使用范围 -->
        <div class="section-title">优惠券使用范围</div>
        <el-form :model="form" label-width="120px" class="coupon-form">
          <div class="form-section">
            <el-form-item
              v-for="(item, index) in formData2"
              :key="index"
              :label="item.label"
              :prop="item.prop"
              style="margin-bottom: 30px"
            >
              <el-radio-group v-model="form[item.prop]" @change="onScopeChange">
                <el-radio
                  v-for="it in item.options"
                  :key="it.value"
                  :label="it.value"
                >
                  {{ it.name }}
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </div>
        </el-form>

        <!-- 使用范围选择区域 - 当选择"指定"时显示 -->
        <div v-if="showScopeSelection" class="scope-selection-area">
          <!-- 搜索区域 -->
          <div class="search-container">
            <PlusSearch
              v-model="scopeForm"
              :columns="searchColumns"
              :show-number="3"
              :label-width="80"
              :hasUnfold="false"
              :searchOnChange="false"
              @search="onSearch"
              @reset="onReset"
            />
          </div>

          <!-- 数据表格 -->
          <div class="table-container">
            <div class="table-title">课程选择</div>
            <pure-table
              ref="tableRef"
              row-key="id"
              adaptive
              :adaptiveConfig="{ offsetBottom: 108 }"
              table-layout="auto"
              :loading="loading"
              :data="tableData"
              :columns="tableColumns"
              :header-cell-style="{
                background: 'var(--el-fill-color-light)',
                color: 'var(--el-text-color-primary)'
              }"
              :reserve-selection="true"
              @selection-change="onSelectionChange"
            />

            <!-- 分页 -->
            <div class="pagination">
              <el-pagination
                background
                layout="total, sizes, prev, pager, next, jumper"
                :current-page="pagination.page + 1"
                :page-size="pagination.size"
                :total="pagination.total"
                :page-sizes="[10, 20, 50, 100]"
                @size-change="onSizeChange"
                @current-change="onPageChange"
              />
            </div>

            <!-- 选择信息提示 -->
            <div class="selection-info">
              <span v-if="showScopeSelection">
                已选择
                <span class="selected-count">{{ selectedRows.length }}</span>
                个课程
              </span>
              <span v-else style="color: #909399">
                通用模式：所有课程均可使用
              </span>
            </div>
          </div>
        </div>
      </div>
      <!-- 表单操作按钮 -->
      <div class="form-actions">
        <el-button @click="reset">取消</el-button>
        <el-button type="primary" :loading="getListLoading" @click="submitForm">
          确认
        </el-button>
      </div>
    </el-scrollbar>
  </div>
</template>

<style lang="scss" scoped>
.scrollbar {
  height: 88vh;
  position: relative;
  // height: calc(88vh - 100px);
}
.coupon-create {
  background-color: #ffffff;
  .common {
    padding: 20px 20px 80px 20px;
    height: calc(88vh - 60px);
    overflow-y: auto;
  }

  .coupon-form {
    margin-left: 80px;
  }

  .form-section {
    margin-top: 40px;
    margin-bottom: 32px;
  }

  .section-title {
    color: #0e1832;
    font-family: Source Han Sans CN;
    font-size: 16px;
    font-weight: 500;
    display: flex;
    align-items: center;
    margin: 20px 0 20px 10px;

    &::after {
      content: "";
      flex: 1;
      height: 1px;
      background: #e4e7ed;
      margin-left: 16px;
    }
  }

  .time-range {
    display: flex;
    align-items: center;

    span {
      color: #606266;
      font-size: 14px;
      white-space: nowrap;
    }
  }

  .discount-input-group {
    display: flex;
    align-items: center;
    gap: 10px;
  }

  .form-text {
    // margin: 0 10px;
    color: #606266;
    font-size: 14px;
  }

  /* 时间选择器样式优化 */
  :deep(.el-date-editor.el-input) {
    width: 100% !important;
  }

  :deep(.el-date-editor.el-input__wrapper) {
    width: 100% !important;
  }

  /* 强制控制日期选择器宽度 */
  :deep(.el-form-item__content .el-date-editor) {
    width: 200px !important;
    max-width: 200px !important;
    min-width: 200px !important;
  }

  /* 确保日期选择器不会超出容器 */
  :deep(.el-date-editor) {
    box-sizing: border-box !important;
  }

  /* 优化日期选择器内部布局 */
  :deep(.el-range-editor .el-range__icon) {
    margin-left: 5px !important;
    margin-right: 5px !important;
  }

  :deep(.el-range-editor .el-range__close-icon) {
    margin-right: 5px !important;
  }

  // /* 强制覆盖 Element Plus 的默认样式 */
  :deep(.el-form-item__content .el-date-editor--daterange) {
    width: 400px !important;
    max-width: 400px !important;
    min-width: 400px !important;
  }

  :deep(.el-form-item__content .el-range-editor) {
    width: 400px !important;
    max-width: 400px !important;
    min-width: 400px !important;
  }

  .form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding-bottom: 20px;
    position: absolute;
    bottom: 0px;
    right: 20px;
    background-color: #ffffff;
    width: 100%;
    // z-index: 9999;
    padding-top: 20px;
  }
}

/* 使用范围选择区域样式 */
.scope-selection-area {
  margin-top: 20px;
}

.search-container {
  padding: 18px 22px;
  background: #fff;
  border-radius: 4px;
  margin-bottom: 16px;
  // border: 1px solid #e4e7ed;

  /* 隐藏搜索和重置按钮上的图标 */
  :deep(.el-form-item__content .el-button .el-icon) {
    display: none;
    width: 0;
    height: 0;
  }

  :deep(.el-form-item__content) {
    .el-button {
      span {
        margin-left: 0px !important;
      }
    }
  }

  /* 确保标签有足够空间 */
  :deep(.el-form-item__label) {
    width: 80px !important;
    text-align: right;
    padding-right: 12px;
    white-space: nowrap;
    overflow: visible;
  }

  /* 优化表单项布局 */
  :deep(.el-form-item) {
    margin-bottom: 16px;
    margin-right: 20px;
    width: 200px;
  }

  /* 确保输入框有足够空间 */
  :deep(.el-form-item__content) {
    flex: 1;
    min-width: 270px;
  }
}

.table-container {
  background: #fff;
  border-radius: 4px;
  margin-bottom: 16px;
  padding: 20px;
  // border: 1px solid #e4e7ed;
}

.table-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 1px solid #ebeef5;
}

.pagination {
  display: flex;
  justify-content: flex-end;
  padding: 12px 20px;
  background-color: #fff;
  border-radius: 4px;
  margin-bottom: 16px;
  // border: 1px solid #e4e7ed;
}

.selection-info {
  text-align: right;
  padding: 12px 20px;
  color: #606266;
  font-size: 14px;
  background-color: #fff;
  border-radius: 4px;
  // border: 1px solid #e4e7ed;
}

.selected-count {
  color: #409eff;
  font-weight: 600;
  font-size: 16px;
}

/* 表格样式 */
:deep(.el-table) {
  border: none;
}

:deep(.el-table__header th) {
  background-color: #fafafa !important;
  color: #606266;
  font-weight: 600;
}

:deep(.el-table__row) {
  border-bottom: 1px solid #ebeef5;
}

:deep(.el-table__row:hover) {
  background-color: #f5f7fa;
}

/* 表单项样式优化 */
:deep(.search-container .el-form-item) {
  margin-bottom: 16px;
}

:deep(.search-container .el-col) {
  max-width: 400px !important;
  min-width: 300px !important;
}

/* 确保搜索区域有足够空间 */
:deep(.search-container .el-row) {
  margin: 0 !important;
}

:deep(.search-container .el-col) {
  padding: 0 10px !important;
}

/* 搜索按钮样式 */
:deep(.search-container .el-button--primary) {
  background-color: #409eff;
  border-color: #409eff;
}

:deep(.search-container .el-button--primary:hover) {
  background-color: #66b1ff;
  border-color: #66b1ff;
}

/* 分页样式 */
:deep(
  .pagination
    .el-pagination.is-background
    .el-pager
    li:not(.is-disabled).is-active
) {
  background-color: #409eff;
}

/* 选择框样式 */
:deep(.el-table__selection) {
  .el-checkbox__inner {
    border-color: #dcdfe6;
  }
}

:deep(.el-table__selection .el-checkbox__input.is-checked .el-checkbox__inner) {
  background-color: #409eff;
  border-color: #409eff;
}
.time-range {
  display: flex;
  align-items: center;

  span {
    color: #606266;
    font-size: 14px;
    white-space: nowrap;
  }
}
</style>
