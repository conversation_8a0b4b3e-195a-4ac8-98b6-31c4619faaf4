<script setup>
import { ref, reactive, onMounted } from "vue";
import { useRouter, useRoute } from "vue-router";
import { ElMessage } from "element-plus";
import CouponDetailInfo from "./components/couponDetailInfo.vue";
import { findReceiveRecordByCouponId } from "@/api/coupon.js";
import dayjs from "dayjs";
import { requestTo } from "@/utils/http/tool";
import { decrypt, encryption } from "@/utils/SM4.js";
import { Hide, View } from "@element-plus/icons-vue";
import { COUPON_SOURCE_TYPE } from "@/utils/enum.js";
import { debounce, isEmpty } from "@iceywu/utils";
defineOptions({
  name: "CouponManagementDetail"
});

const router = useRouter();
const route = useRoute();
// 搜索表单数据
const searchForm = reactive({
  phone: "",
  couponSource: "", //获取方式
  couponState: "" //优惠券状态
});

// 表格列配置
const columns = ref([
  {
    label: "用户账号",
    prop: "phone",
    minWidth: 120,
    slot: "phone"
  },
  {
    label: "昵称",
    prop: "name",
    minWidth: 100,
    formatter: ({ name }) => {
      return name || "--";
    }
  },
  {
    label: "领取时间",
    prop: "receiveTime",
    minWidth: 150,
    formatter: ({ receiveTime }) => {
      return receiveTime ? dayjs(receiveTime).format("YYYY-MM-DD") : "--";
    }
  },
  {
    label: "使用时间",
    prop: "useTime",
    minWidth: 150,
    formatter: ({ useTime }) => {
      return useTime ? dayjs(useTime).format("YYYY-MM-DD") : "--";
    }
  },
  {
    label: "优惠券费用类型",
    prop: "feeType",
    minWidth: 120,
    formatter: ({ feeType }) => {
      return feeType ? COUPON_FREE_TYPE[feeType].label : "--";
    }
  },
  {
    label: "优惠课期",
    prop: "coursePeriodName",
    minWidth: 150,
    formatter: ({ coursePeriodName }) => {
      return coursePeriodName || "--";
    }
  },
  {
    label: "获取方式",
    prop: "couponSource",
    minWidth: 100,
    formatter: ({ couponSource }) => {
      return COUPON_SOURCE_TYPE[couponSource]?.label || "--";
    }
  },
  {
    label: "操作",
    fixed: "right",
    width: 180,
    slot: "operation"
  }
]);
const params = ref({
  page: 1,
  size: 15,
  sort: "createdAt,desc"
});
// 表格数据
const tableData = ref([]);
const loading = ref(false);
// 列表表格 api
const onSearch = async val => {
  const paramsData = {
    page: params.value.page - 1,
    size: params.value.size,
    sort: params.value.sort,
    couponId: route.query.id
  };
  for (const paramsDataKey in searchForm) {
    let isArray = Array.isArray(searchForm[paramsDataKey]);
    if (isArray) {
      if (searchForm[paramsDataKey].length > 0) {
        paramsData[paramsDataKey] = searchForm[paramsDataKey];
      }
    } else {
      if (paramsDataKey === "freeze") {
        if (searchForm[paramsDataKey] !== "all") {
          paramsData[paramsDataKey] = searchForm[paramsDataKey];
        }
      } else if (searchForm[paramsDataKey]) {
        paramsData[paramsDataKey] = searchForm[paramsDataKey];
      }
    }
  }
  if (searchForm.phone) {
    paramsData.phone = encryption(searchForm.phone);
  } else {
    delete paramsData.phone;
  }
  const [err, res] = await requestTo(findReceiveRecordByCouponId(paramsData));
  if (res) {
    // console.log("🍪res------------------------------>", res);
    tableData.value = (res.content || []).map(item => ({
      ...item,
      isPhoneVisible: false
    }));
    pagination.total = res.totalElements;
  }
  if (err) {
    console.error(err);
  }
};
// 手机号切换显示逻辑
const togglePhoneDisplay = row => {
  row.isPhoneVisible = !row.isPhoneVisible;
};
// 分页配置
const pagination = {
  total: 0,
  pageSize: 15,
  currentPage: 1,
  background: true,
  pageSizes: [15, 30, 50, 100]
};

// 统计数据
const statistics = ref({
  totalIssue: 0, //发放数
  usedNumber: 0, //使用数
  receivedNumber: 0 //领取数
});

// 搜索处理
const handleSearch = () => {
  params.value.page = 1;
  onSearch();
};

// 重置搜索
const handleReset = () => {
  params.value.page = 1;
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = "";
  });
  onSearch();
};
// 每页多少条
const handleSizeChange = val => {
  pagination.pageSize = val;
  params.value.size = val;
  onSearch();
};
// 前往页数
const handleCurrentChange = val => {
  pagination.currentPage = val;
  params.value.page = val;
  onSearch();
};
// 返回
const handleBack = () => {
  router.replace("/course/coupon/index");
};
const couponInfoEvt = val => {
  statistics.value.receivedNumber = val.receivedNumber;
  statistics.value.usedNumber = val.usedNumber;
  statistics.value.totalIssue = val.totalIssue;
};
// 关联订单
const orderEvt = row => {
  if (isEmpty(row.orderId)) {
    ElMessage({
      message: "该优惠券没有订单",
      type: "error"
    });
  } else {
    router.push({
      path: "/course/coupon/detail/order",
      query: {
        ordersId: row.orderId
      }
    });
  }
};
onMounted(() => {
  onSearch();
});
</script>

<template>
  <div class="coupon-detail">
    <!-- 优惠券详情展示区域 -->
    <div class="common detail-container">
      <CouponDetailInfo
        :id="route.query.id"
        :valid="route.query.valid"
        @coupon-info="couponInfoEvt"
      />
    </div>

    <!-- 表格区域 -->
    <div class="common table-container">
      <!-- 搜索区域 -->
      <div class="search-section">
        <div class="coupon-search-container">
          <div class="search-items">
            <div class="search-item">
              <span class="search-label">用户账号</span>
              <el-input
                v-model="searchForm.phone"
                placeholder="请输入用户账号"
                style="width: 200px"
                clearable
              />
            </div>
            <div class="search-item">
              <span class="search-label">获取方式</span>
              <el-select
                v-model="searchForm.couponSource"
                placeholder="请选择获取方式"
                style="width: 150px"
                clearable
              >
                <el-option label="全部" value="" />
                <el-option label="在线领取" value="ONLINE" />
                <el-option label="手动派发" value="MANUAL" />
              </el-select>
            </div>
            <div class="search-item">
              <span class="search-label">优惠券状态</span>
              <el-select
                v-model="searchForm.couponState"
                placeholder="请选择状态"
                style="width: 150px"
                clearable
              >
                <el-option label="全部" value="" />
                <el-option label="未使用" value="NOT_USED" />
                <el-option label="已使用" value="USED" />
                <el-option label="已过期" value="EXPIRED" />
              </el-select>
            </div>
          </div>
          <div class="search-buttons">
            <el-button type="primary" @click="handleSearch">搜索</el-button>
            <el-button @click="handleReset">重置</el-button>
          </div>
        </div>
      </div>

      <!-- 标题和统计信息 -->
      <div class="title-statistics">
        <div class="table-title">
          <h3>领取记录</h3>
        </div>
        <div class="statistics">
          <span class="stat-item">发放数量：{{ statistics.totalIssue }}</span>
          <span class="stat-item">领取数量：{{ statistics.receivedNumber }}</span>
          <span class="stat-item">使用数量：{{ statistics.usedNumber }}</span>
        </div>
      </div>

      <!-- 表格 -->
      <div class="table-section">
        <pure-table
          ref="tableRef"
          row-key="id"
          adaptive
          :adaptiveConfig="{ offsetBottom: 180 }"
          align-whole="left"
          table-layout="auto"
          :data="tableData"
          :columns="columns"
          :loading="loading"
          :pagination="pagination"
          :header-cell-style="{
            background: 'var(--el-fill-color-light)',
            color: 'var(--el-text-color-primary)'
          }"
          @page-size-change="handleSizeChange"
          @page-current-change="handleCurrentChange"
        >
          <template #phone="{ row }">
            <div v-if="row.phone" class="phone-display">
              <span class="phone-text">
                {{
                  row.isPhoneVisible
                    ? decrypt(row.phone)
                    : row.phoneCt || row.phone
                }}
              </span>
              <el-icon
                v-if="row.phoneCt"
                class="phone-toggle-icon"
                @click="togglePhoneDisplay(row)"
              >
                <component :is="row.isPhoneVisible ? View : Hide" />
              </el-icon>
            </div>
            <span v-else>--</span>
          </template>
          <template #operation="{ row }">
            <div v-if="row.orderId" class="operation-buttons">
              <el-button
                class="reset-margin"
                link
                type="primary"
                @click="orderEvt(row)"
              >
                关联订单
              </el-button>
            </div>
          </template>
        </pure-table>
      </div>

      <!-- 返回按钮 -->
      <div class="footer-actions">
        <el-button @click="handleBack">返回</el-button>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.coupon-detail {
  height: 88vh;
  display: flex;
  flex-direction: column;
  .common {
    padding: 20px;
    background-color: #fff;
    margin-bottom: 20px;

    &.table-container {
      flex: 1;
      display: flex;
      flex-direction: column;
    }
  }

  .detail-card {
    padding: 4px 0;
  }

  .detail-row {
    display: flex;
    margin-bottom: 16px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .detail-item {
    flex: 1;
    display: flex;
    align-items: center;

    .label {
      color: #606266;
      font-size: 14px;
      min-width: 120px;
      flex-shrink: 0;
    }

    .value {
      color: #303133;
      font-size: 14px;
      font-weight: 500;
    }
  }

  .search-section {
    margin-bottom: 20px;
    flex-shrink: 0;
  }

  .coupon-search-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .search-items {
    display: flex;
    gap: 20px;
    align-items: center;
  }

  .search-item {
    display: flex;
    align-items: center;
    gap: 8px;

    .search-label {
      font-size: 14px;
      color: #606266;
      white-space: nowrap;
    }
  }

  .title-statistics {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 12px;
    border-bottom: 1px solid #e4e7ed;
    flex-shrink: 0;
  }

  .table-title {
    h3 {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
      color: #303133;
    }
  }

  .statistics {
    display: flex;
    gap: 40px;

    .stat-item {
      font-size: 14px;
      color: #303133;
      font-weight: 500;
    }
  }

  .table-section {
    flex: 1;
    overflow: hidden;
  }

  .operation-buttons {
    display: flex;
    gap: 8px;
  }

  .footer-actions {
    display: flex;
    justify-content: flex-end;
    margin-top: 20px;
  }
}
.phone-display {
  display: flex;
  align-items: center;
  gap: 8px;

  .phone-text {
    min-width: 90px;
  }

  .phone-toggle-icon {
    cursor: pointer;
    font-size: 16px;
  }
}
</style>
