<script setup>
import { ref, reactive, onMounted, nextTick } from "vue";
import { useRouter, useRoute } from "vue-router";
import { PlusSearch, PlusTable } from "plus-pro-components";
import { ArrowLeft, Search } from "@element-plus/icons-vue";
import { getCouponCourseList } from "@/api/coupon.js";
import { ElMessage } from "element-plus";

defineOptions({
  name: "ScopeOfApplicationDetail"
});

// 防抖函数
const debounce = (func, wait) => {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
};

const router = useRouter();
const route = useRoute();

// 计算表格高度
const searchFormHeight = ref(0);
const tableHeight = ref("calc(100vh - 350px)");

const calculateTableHeight = async () => {
  await nextTick();
  const searchForm = document.querySelector(".con-content");
  if (searchForm) {
    searchFormHeight.value = searchForm.offsetHeight;
    // 动态计算表格高度，减去搜索框高度、表头按钮高度、分页器高度和其他间距
    tableHeight.value = `calc(100vh - 240px - ${searchFormHeight.value}px)`;
  }
};

// 搜索表单数据
const searchForm = reactive({
  courseName: "",
  coursePeriodName: "",
  coursePeriodState: ""
});

// 搜索列配置
const searchColumns = ref([
  {
    label: "课程名称",
    prop: "courseName",
    valueType: "input",
    placeholder: "请输入课程名称"
  },
  {
    label: "课期名称",
    prop: "coursePeriodName",
    valueType: "input",
    placeholder: "请输入课期名称"
  },
  {
    label: "课期状态",
    prop: "coursePeriodState",
    valueType: "select",
    fieldProps: {
      placeholder: "全部",
      clearable: true,
      style: { width: "200px" }
    },
    options: [
      {
        label: "全部",
        value: ""
      },
      {
        value: "ONLINE",
        label: "上架"
      },
      {
        value: "NOT_LISTED",
        label: "未上架"
      },
      {
        value: "ONLINE_UNDER_REVIEW",
        label: "上架审核中"
      },
      {
        value: "OFFLINE",
        label: "下架"
      },
      {
        value: "OFFLINE_UNDER_REVIEW",
        label: "下架审核中"
      },
      {
        value: "COMPLETED",
        label: "已完成"
      }
    ]
  }
]);

// 表格列配置 - 根据 API 返回的数据结构调整
const tableColumns = ref([
  {
    label: "课程名称",
    prop: "courseName",
    minWidth: 200,
    render: (cal, row) => {
      return row.row.course?.name || "-";
    }
  },
  {
    label: "课期名称",
    prop: "coursePeriodName",
    minWidth: 150,
    render: (cal, row) => {
      return row.row.name || "-";
    }
  },
  {
    label: "期号",
    prop: "termNumber",
    minWidth: 80,
    align: "center"
  },
  {
    label: "开课时间",
    prop: "openTime",
    minWidth: 120,
    align: "center",
    render: (cal, row) => {
      if (row.row.openTime) {
        return new Date(row.row.openTime).toLocaleDateString("zh-CN");
      }
      return "-";
    }
  },
  {
    label: "购买类型",
    prop: "buyType",
    minWidth: 100,
    align: "center",
    render: (cal, row) => {
      const buyTypeMap = {
        ORDINARY: "普通单",
        PRIVATE_DOMAIN_GROUP_ORDER: "私域团购单"
      };
      return buyTypeMap[row.row.buyType] || row.row.buyType || "-";
    }
  },
  {
    label: "课期状态",
    prop: "coursePeriodState",
    minWidth: 100,
    align: "center",
    render: (cal, row) => {
      const statusMap = {
        ONLINE: "上架",
        NOT_LISTED: "未上架",
        ONLINE_UNDER_REVIEW: "上架审核中",
        OFFLINE: "下架",
        OFFLINE_UNDER_REVIEW: "下架审核中",
        COMPLETED: "已完成"
      };
      const status = statusMap[row.row.coursePeriodState] || coursePeriodState;
      return status;
    }
  }
]);

// 表格数据
const tableData = ref([]);

// 分页配置
const pagination = reactive({
  page: 1,
  size: 10,
  total: 0
});

// 加载状态
const loading = ref(false);

// 获取优惠券课程列表
const fetchCouponCourseList = async () => {
  loading.value = true;
  try {
    // 构建查询参数
    const params = {
      page: pagination.page - 1, // API 使用从0开始的页码
      size: pagination.size,
      couponId: route.query?.id,
      ...searchForm
    };

    // 过滤空值
    Object.keys(params).forEach(key => {
      if (
        params[key] === "" ||
        params[key] === null ||
        params[key] === undefined
      ) {
        delete params[key];
      }
    });

    const response = await getCouponCourseList(params);

    if (response.code === 0 || response.code === 200) {
      tableData.value = response.data.content || [];
      pagination.total = response.data.totalElements || 0;
      pagination.page = (response.data.number || 0) + 1; // 转换为从1开始的页码
    } else {
      ElMessage.error(response.msg || "获取数据失败");
      tableData.value = [];
      pagination.total = 0;
    }
  } catch (error) {
    console.error("获取优惠券课程列表失败:", error);
    ElMessage.error("获取数据失败，请稍后重试");
    tableData.value = [];
    pagination.total = 0;
  } finally {
    loading.value = false;
  }
};

// 搜索方法
const onSearch = () => {
  pagination.page = 1; // 重置到第一页
  fetchCouponCourseList();
};

// 防抖搜索方法
const debouncedSearch = debounce(() => {
  onSearch();
}, 500);

// 搜索输入变化处理
const onSearchInputChange = () => {
  // 如果用户正在输入，延迟搜索
  debouncedSearch();
};

// 重置方法
const onReset = () => {
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = key === "coursePeriodState" ? "" : "";
  });
  pagination.page = 1;
  fetchCouponCourseList();
};

// 分页改变
const onPageChange = page => {
  pagination.page = page;
  fetchCouponCourseList();
};

// 每页条数改变
const onSizeChange = size => {
  pagination.size = size;
  pagination.page = 1;
  fetchCouponCourseList();
};

// 返回上一页
const goBack = () => {
  router.go(-1);
};

onMounted(() => {
  // 页面加载时获取数据
  calculateTableHeight();
  window.addEventListener("resize", calculateTableHeight);

  // 初始加载数据
  fetchCouponCourseList();
});
</script>

<template>
  <div class="page-container">
    <div class="containers">
      <!-- 搜索区域 -->
      <div class="con_search">
        <PlusSearch
          v-model="searchForm"
          :columns="searchColumns"
          :show-number="3"
          :label-width="80"
          :hasUnfold="false"
          :searchOnChange="false"
          :label-suffix="''"
          @search="onSearch"
          @reset="onReset"
        />
      </div>

      <div class="con-content" :max-height="tableHeight">
        <!-- 数据表格 -->
        <div class="table-container">
          <!-- <div class="table-title">优惠券使用范围</div> -->
          <PlusTable
            v-loading="loading"
            :data="tableData"
            :columns="tableColumns"
            :title-bar="false"
            :border="false"
            class="no-border-table"
            :empty-text="loading ? '加载中...' : '暂无数据'"
          />
        </div>

        <!-- 分页 -->
        <div class="pagination">
          <el-pagination
            background
            layout="total, sizes, prev, pager, next, jumper"
            :current-page="pagination.page"
            :page-size="pagination.size"
            :total="pagination.total"
            :page-sizes="[10, 20, 50, 100]"
            @size-change="onSizeChange"
            @current-change="onPageChange"
          />
        </div>
      </div>

      <!-- 返回按钮 -->
      <div class="action-buttons">
        <el-button @click="goBack"> 返回 </el-button>
      </div>
    </div>
  </div>
</template>

<style scoped>
.breadcrumb {
  padding: 16px 20px;
  background: #fff;
  border-radius: 4px;
  margin-bottom: 16px;
}

.con_search {
  padding: 18px 22px;
  background: #fff;
  border-radius: 4px;
  margin-bottom: 10px;

  /* 隐藏搜索和重置按钮上的图标 */
  :deep(.el-form-item__content .el-button .el-icon) {
    display: none;
    width: 0;
    height: 0;
  }

  :deep(.el-form-item__content) {
    .el-button {
      span {
        margin-left: 0px !important;
      }
    }
  }
}

.table-container {
  background: #ffffff;
  border-radius: 4px;
  margin-bottom: 16px;
  padding: 20px;
  height: 600px;
}

.pagination {
  display: flex;
  justify-content: flex-end;
  padding: 12px 20px;
  background-color: #fff;
  border-radius: 4px;
  margin-bottom: 16px;
}

.action-buttons {
  display: flex;
  justify-content: flex-end;
  padding: 0 20px 10px 0px;
  background-color: #fff;
}

/* 去掉表格边框 */
:deep(.el-table) {
  border: none;
}

:deep(.el-table__header th) {
  background-color: #fafafa !important;
  color: #606266;
  font-weight: 600;
}

:deep(.el-table__row) {
  border-bottom: 1px solid #ebeef5;
}

:deep(.el-table__row:hover) {
  background-color: #f5f7fa;
}

/* 表单项样式优化 */
:deep(.el-form-item) {
  margin-bottom: 16px;
}

:deep(.el-col) {
  max-width: 330px !important;
}

/* 搜索按钮样式 */
:deep(.el-button--primary) {
  background-color: #409eff;
  border-color: #409eff;
}

:deep(.el-button--primary:hover) {
  background-color: #66b1ff;
  border-color: #66b1ff;
}

/* 分页样式 */
:deep(.el-pagination.is-background .el-pager li:not(.is-disabled).is-active) {
  background-color: #409eff;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .containers {
    padding: 10px;
  }

  .con_search {
    padding: 15px;
  }

  .table-container {
    padding: 15px;
  }
}
.con-content {
  background-color: #fff;
  height: 680px;
}

/* 搜索统计信息样式 */
.search-stats {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  padding: 12px 20px;
  margin-bottom: 16px;
  margin-top: 16px;
}

.stats-text {
  color: #6c757d;
  font-size: 14px;
}

.stats-text strong {
  color: #495057;
  font-weight: 600;
}

/* 无搜索结果提示样式 */
.no-result-tip {
  background: #fff;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  padding: 40px 20px;
  margin-bottom: 16px;
  text-align: center;
}

.tip-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.tip-icon {
  font-size: 48px;
  color: #dee2e6;
}

.tip-text {
  font-size: 16px;
  color: #6c757d;
  margin: 0;
}

.tip-suggestion {
  font-size: 14px;
  color: #adb5bd;
  margin: 0;
}
</style>
