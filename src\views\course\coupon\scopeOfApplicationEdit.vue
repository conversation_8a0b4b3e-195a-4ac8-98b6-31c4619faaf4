<script setup>
import { ref, reactive, onMounted, nextTick } from "vue";
import { useRouter, useRoute } from "vue-router";
import { PlusSearch } from "plus-pro-components";
import { ElMessage, ElMessageBox } from "element-plus";
import CouponDetailInfo from "./components/couponDetailInfo.vue";
import {
  getCouponCourseList,
  getCouponCourse,
  createOrUpdateCoupon,
  getCouponFindById
} from "@/api/coupon.js";

defineOptions({
  name: "ScopeOfApplicationEdit"
});

const router = useRouter();
const route = useRoute();

// 搜索表单数据
const searchForm = reactive({
  courseName: "",
  coursePeriodName: "",
  coursePeriodState: ""
});

// 搜索列配置
const searchColumns = ref([
  {
    label: "课程名称",
    prop: "courseName",
    valueType: "input",
    placeholder: "请输入课程名称"
  },
  {
    label: "课期名称",
    prop: "coursePeriodName",
    valueType: "input",
    placeholder: "请输入课期名称"
  },
  {
    label: "课期状态",
    prop: "coursePeriodState",
    valueType: "select",
    fieldProps: {
      placeholder: "全部",
      clearable: true,
      style: { width: "200px" }
    },
    options: [
      {
        label: "全部",
        value: ""
      },
      {
        value: "ONLINE",
        label: "上架"
      },
      {
        value: "NOT_LISTED",
        label: "未上架"
      },
      {
        value: "ONLINE_UNDER_REVIEW",
        label: "上架审核中"
      },
      {
        value: "OFFLINE",
        label: "下架"
      },
      {
        value: "OFFLINE_UNDER_REVIEW",
        label: "下架审核中"
      },
      {
        value: "COMPLETED",
        label: "已完成"
      }
    ]
  }
]);

// 表格数据
const tableData = ref([]);

// 分页配置
const pagination = reactive({
  page: 0, // API从0开始
  size: 10,
  total: 0
});

// 加载状态
const loading = ref(false);

// 选中的行
const selectedRows = ref([]);

// 存储选中行的详细信息
const rowList = ref({
  coursePeriodIds: [],
  couponId: null
});

// 表格引用
const tableRef = ref(null);

// 获取表格数据
const fetchTableData = async () => {
  loading.value = true;
  try {
    const params = {
      page: pagination.page,
      size: pagination.size,
      couponId: route.query?.id,
      ...searchForm
    };

    // 过滤空值
    Object.keys(params).forEach(key => {
      if (
        params[key] === "" ||
        params[key] === null ||
        params[key] === undefined
      ) {
        delete params[key];
      }
    });

    const response = await getCouponCourse(params);

    if (response.code === 200) {
      tableData.value = response.data.content || [];
      pagination.total = response.data.totalElements || 0;
      pagination.page = response.data.number || 0;
      pagination.size = response.data.size || 20;

      // 数据加载完成后，自动勾选已选择的优惠券
      nextTick(() => {
        autoSelectExistingCoupons();
      });
    } else {
      ElMessage.error(response.msg || "获取数据失败");
    }
  } catch (error) {
    console.error("获取数据失败:", error);
    ElMessage.error("获取数据失败，请稍后重试");
  } finally {
    loading.value = false;
  }
};

// 自动勾选已选择的优惠券
const autoSelectExistingCoupons = async () => {
  if (!tableData.value.length) return;

  try {
    // 获取当前优惠券的使用范围课期列表
    const couponId = route.query?.id;
    if (!couponId) {
      console.warn("🍪-----未找到优惠券ID");
      return;
    }

    // 调用新的接口 getCouponCourseList 获取优惠券的使用范围课期
    const couponResponse = await getCouponCourseList({
      couponId: couponId,
      page: 0,
      size: 1000, // 获取足够多的数据来确保覆盖所有已选择的课期
      sort: "createdAt,desc" // 按创建时间倒序排列
    });

    if (couponResponse.code !== 200 || !couponResponse.data) {
      console.warn("🍪-----获取优惠券使用范围失败");
      return;
    }

    const couponData = couponResponse.data;
    const coursePeriods = couponData.content || [];

    if (coursePeriods.length === 0) {
      return;
    }

    // 从coursePeriods中提取ID
    const existingIds = coursePeriods.map(item => item.id);

    // 找到需要勾选的行（在tableData中匹配ID）
    const rowsToSelect = tableData.value.filter(row =>
      existingIds.includes(row.id)
    );

    if (rowsToSelect.length > 0) {
      // 设置选中的行
      selectedRows.value = rowsToSelect;

      // 更新rowList
      rowList.value = {
        coursePeriodIds: rowsToSelect.map(row => row.id),
        couponId: Number(couponId)
      };

      // 使用 nextTick 确保表格已渲染完成
      await nextTick();

      // 对于pure-table组件，尝试多种方式设置选中状态
      try {
        if (tableRef.value?.getTableRef) {
          const elTable = tableRef.value.getTableRef();
          if (elTable && elTable.toggleRowSelection) {
            rowsToSelect.forEach(row => {
              elTable.toggleRowSelection(row, true);
            });
          }
        }
      } catch (selectionError) {
        console.error("🍪-----设置选中状态失败:", selectionError);
      }
    }
  } catch (error) {
    console.error("🍪-----自动勾选失败:", error);
  }
};

// 搜索方法
const onSearch = () => {
  pagination.page = 0; // 搜索时重置到第一页
  // 清空选择状态
  selectedRows.value = [];
  rowList.value = {
    coursePeriodIds: [],
    couponId: null
  };
  fetchTableData();
};

// 重置方法
const onReset = () => {
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = "";
  });
  pagination.page = 0;
  // 清空选择状态
  selectedRows.value = [];
  rowList.value = {
    coursePeriodIds: [],
    couponId: null
  };
  fetchTableData();
};

// 分页改变
const onPageChange = page => {
  pagination.page = page - 1; // 转换为API的从0开始
  // 清空选择状态
  selectedRows.value = [];
  rowList.value = {
    coursePeriodIds: [],
    couponId: null
  };
  fetchTableData();
};

// 每页条数改变
const onSizeChange = size => {
  pagination.size = size;
  pagination.page = 0;
  // 清空选择状态
  selectedRows.value = [];
  rowList.value = {
    coursePeriodIds: [],
    couponId: null
  };
  fetchTableData();
};

// 选择改变
const onSelectionChange = selection => {
  selectedRows.value = selection || [];

  // 更新rowList，存储选中行的详细信息
  if (selection && selection.length > 0) {
    rowList.value = {
      coursePeriodIds: selection.map(row => row.id),
      couponId: Number(route.query?.id) // 所有选中行的ID
    };
  } else {
    // 没有选择时，清空rowList
    rowList.value = {
      coursePeriodIds: [],
      couponId: null
    };
  }
};

// 确认修改
const onConfirm = async () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning("请至少选择一个课期");
    return;
  }

  try {
    // 确认对话框
    await ElMessageBox.confirm(
      `确定要为选中的 ${selectedRows.value.length} 个课期设置优惠券使用范围吗？`,
      "确认操作",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }
    );

    loading.value = true;
    const operateLog = {
      operateLogType: "COURSE_MANAGEMENT",
      operateType: `修改了${selectedRows.value.length}条使用范围信息`
    };

    const { code, data } = await createOrUpdateCoupon(
      rowList.value,
      operateLog
    );

    if (code === 200) {
      ElMessage.success(`成功修改优惠券使用范围`);
      // 重新加载数据
      fetchTableData();
      // 清空选择
      selectedRows.value = [];
      rowList.value = {
        coursePeriodIds: [],
        couponId: null
      };
      router.go(-1);
    }
    // else {
    //   ElMessage.warning(`成功: ${successCount} 个，失败: ${failCount} 个`);
    // }
    // }
  } catch (error) {
    if (error !== "cancel") {
      console.error("修改失败:", error);
      ElMessage.error("修改失败，请稍后重试");
    }
  } finally {
    loading.value = false;
  }
};

// 取消操作
const onCancel = () => {
  // ElMessage.info("已取消修改");
  router.go(-1);
};

// 表格列配置
const tableColumns = ref([
  {
    type: "selection",
    width: 55,
    reserveSelection: true
  },
  {
    label: "期号",
    prop: "termNumber",
    width: 100,
    align: "center"
  },
  {
    label: "课期名称",
    prop: "name"
  },
  {
    label: "开课时间",
    prop: "openTime",
    width: 220,
    formatter: ({ openTime }) => {
      if (openTime) {
        return new Date(openTime).toLocaleDateString("zh-CN");
      }
      return "-";
    }
  },
  {
    label: "购买类型",
    prop: "buyType",
    width: 100,
    formatter: ({ buyType }) => {
      const buyTypeMap = {
        ORDINARY: "普通单",
        PRIVATE_DOMAIN_GROUP_ORDER: "私域团购单"
      };
      return buyTypeMap[buyType] || buyType || "-";
    }
  },
  {
    label: "课期状态",
    prop: "coursePeriodState",
    width: 100,
    formatter: ({ coursePeriodState }) => {
      const statusMap = {
        ONLINE: "上架",
        NOT_LISTED: "未上架",
        ONLINE_UNDER_REVIEW: "上架审核中",
        OFFLINE: "下架",
        OFFLINE_UNDER_REVIEW: "下架审核中",
        COMPLETED: "已完成"
      };
      const status = statusMap[coursePeriodState] || coursePeriodState;
      return status;
    }
  }
]);

onMounted(() => {
  // 初始加载数据
  fetchTableData();
});
</script>

<template>
  <div class="page-container">
    <div class="containers">
      <!-- 优惠券详情 -->
      <div class="coupon-detail">
        <CouponDetailInfo :id="route.query.id" :showUseScope="false" />
      </div>

      <div class="con-content">
        <!-- 搜索区域 -->
        <div class="con_search">
          <PlusSearch
            v-model="searchForm"
            :columns="searchColumns"
            :show-number="3"
            :label-width="80"
            :hasUnfold="false"
            :searchOnChange="false"
            @search="onSearch"
            @reset="onReset"
          />
        </div>

        <!-- 数据表格 -->
        <div class="table-container">
          <pure-table
            ref="tableRef"
            row-key="id"
            adaptive
            :adaptiveConfig="{ offsetBottom: 108 }"
            table-layout="auto"
            :loading="loading"
            :data="tableData"
            :columns="tableColumns"
            :header-cell-style="{
              background: 'var(--el-fill-color-light)',
              color: 'var(--el-text-color-primary)'
            }"
            :reserve-selection="true"
            @selection-change="onSelectionChange"
          />
        </div>

        <!-- 分页 -->
        <div class="pagination">
          <el-pagination
            background
            layout="total, sizes, prev, pager, next, jumper"
            :current-page="pagination.page + 1"
            :page-size="pagination.size"
            :total="pagination.total"
            :page-sizes="[10, 20, 50, 100]"
            width="100%"
            @size-change="onSizeChange"
            @current-change="onPageChange"
          />
        </div>

        <!-- 操作按钮 -->
        <div class="action-buttons">
          <el-button @click="onCancel">取消</el-button>
          <el-button type="primary" @click="onConfirm"> 确认 </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.breadcrumb {
  padding: 16px 20px;
  background: #fff;
  border-radius: 4px;
  margin-bottom: 16px;
}

.coupon-detail {
  background: #fff;
  border-radius: 4px;
  margin-bottom: 16px;
  padding: 20px;
}

.label {
  color: #606266;
  font-weight: 500;
  min-width: 120px;
  margin-right: 8px;
}

.value {
  color: #303133;
  flex: 1;
}

.status-enabled {
  color: #67c23a;
  font-weight: 500;
}

.con_search {
  padding: 15px 22px 0 22px;
  background: #fff;
  border-radius: 4px;
  /* margin-bottom: 16px; */

  /* 隐藏搜索和重置按钮上的图标 */
  :deep(.el-form-item__content .el-button .el-icon) {
    display: none;
    width: 0;
    height: 0;
  }

  :deep(.el-form-item__content) {
    .el-button {
      span {
        margin-left: 0px !important;
      }
    }
  }
}

.table-container {
  background: #ffffff;
  border-radius: 4px;
  margin-bottom: 16px;
  padding: 20px;
  height: 380px;
  overflow-y: auto;
  scrollbar-width: none;
}

.pagination {
  display: flex;
  justify-content: flex-end;
  background-color: #fff;
  border-radius: 4px;
  margin: 0px 20px 16px 0;
}

.action-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 16px;
  padding: 0px 20px 0px 0;
  background-color: #fff;
}

/* 表格样式 */
:deep(.el-table) {
  border: none;
}

:deep(.el-table__header th) {
  background-color: #fafafa !important;
  color: #606266;
  font-weight: 600;
}

:deep(.el-table__row) {
  border-bottom: 1px solid #ebeef5;
}

:deep(.el-table__row:hover) {
  background-color: #f5f7fa;
}

/* pure-table 样式 */
:deep(.pure-table) {
  width: 100% !important;
  display: flex;
  flex-direction: column;

  .el-table__body-wrapper {
    overflow-y: auto;
  }
}

/* 表单项样式优化 */
:deep(.el-form-item) {
  margin-bottom: 16px;
}

:deep(.el-col) {
  max-width: 330px !important;
}

/* 搜索按钮样式 */
:deep(.el-button--primary) {
  background-color: #409eff;
  border-color: #409eff;
}

:deep(.el-button--primary:hover) {
  background-color: #66b1ff;
  border-color: #66b1ff;
}

/* 分页样式 */
:deep(.el-pagination.is-background .el-pager li:not(.is-disabled).is-active) {
  background-color: #409eff;
}

/* 选择框样式 */
:deep(.el-table__selection) {
  .el-checkbox__inner {
    border-color: #dcdfe6;
  }
}

:deep(.el-table__selection .el-checkbox__input.is-checked .el-checkbox__inner) {
  background-color: #409eff;
  border-color: #409eff;
}
.con-content {
  height: 580px;
  background-color: #ffffff;
}
</style>
