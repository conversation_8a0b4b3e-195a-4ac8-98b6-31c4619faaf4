import { ref, reactive, computed, nextTick, watch, onMounted } from "vue";
import { useRoute, useRouter } from "vue-router";
import { ElMessage, ElMessageBox } from "element-plus";
import { debounce, removeEmptyValues } from "@iceywu/utils";
import { addCoupon, getCouponCourse, getCouponFindById } from "@/api/coupon.js";
import { formatTime } from "@/utils/index";
// import { removeEmptyValues } from "@iceywu/utils";
// import { ElMessage } from "element-plus";

export default function useAddCoupon() {
  const router = useRouter();
  const route = useRoute();

  // 表单数据 - 按照 create.vue 的字段调整
  const form = reactive({
    name: "", // 优惠券名称
    feeType: "CLASS_HOUR", // 费用类型: CLASS_HOUR, MATERIAL - 默认选择课时
    couponDiscountType: "FULL_REDUCTION", // 优惠券类型，默认选中满减券
    conditionAmount: "", // 满减条件金额
    discountAmount: "", // 优惠金额
    totalIssue: "", // 发放数量
    distributionStartTime: "", // 发放开始时间
    distributionEndTime: "", // 发放结束时间
    isUseLimit: true, // 使用是否有限制
    startTime: "", // 使用开始时间
    endTime: "", // 使用结束时间
    remarks: "", // 备注
    enabled: true, // 状态开关
    coursePeriodIds: [], // 可使用课期ID数组
    couponScope: "ALL", // 优惠券使用范围: ALL(通用),LIMIT(指定)
    usedCourse: "ALL", // 前端使用范围标识 - 默认设置为'ALL'(通用)

    // 新增字段用于处理使用时间逻辑
    useTimeType: 1, // 使用时间类型: 1-不限, 2-有限
    useTimeRange: [], // 使用时间范围(当useTimeType=2时使用)
    distributionTime: [] // 发放时间范围
  });

  const formRef = ref(null);
  const richFlag = ref(false);

  // 表单验证规则 - 按照 create.vue 的校验规则调整
  const rules = {
    name: [
      { required: true, message: "请输入优惠券名称", trigger: "blur" },
      {
        min: 1,
        max: 10,
        message: "优惠券名称长度在 1 到 10 个字符",
        trigger: "blur"
      }
    ],
    feeType: [{ required: true, message: "请选择费用类型", trigger: "change" }],
    couponDiscountType: [
      { required: true, message: "请选择优惠类型", trigger: "change" }
    ],
    totalIssue: [
      { required: true, message: "请输入发放数量", trigger: "blur" },
      {
        validator: (rule, value, callback) => {
          if (value < 1) {
            callback(new Error("发放数量不能小于1"));
          } else if (value > 1000000) {
            callback(new Error("发放数量不能超过1,000,000"));
          } else {
            callback();
          }
        },
        trigger: "blur"
      }
    ],
    distributionStartTime: [
      {
        validator: (rule, value, callback) => {
          if (value && form.distributionEndTime) {
            if (new Date(value) >= new Date(form.distributionEndTime)) {
              callback(new Error("发放开始时间必须早于发放结束时间"));
            } else {
              callback();
            }
          } else {
            callback();
          }
        },
        trigger: "change"
      }
    ],
    distributionEndTime: [
      {
        validator: (rule, value, callback) => {
          if (value && form.distributionStartTime) {
            if (new Date(value) <= new Date(form.distributionStartTime)) {
              callback(new Error("发放结束时间必须晚于发放开始时间"));
            } else {
              callback();
            }
          } else {
            callback();
          }
        },
        trigger: "change"
      }
    ],
    startTime: [
      {
        validator: (rule, value, callback) => {
          if (form.useTimeType === 2) {
            // 验证使用开始时间不能早于发放开始时间
            if (value && form.distributionStartTime) {
              if (new Date(value) < new Date(form.distributionStartTime)) {
                callback(new Error("使用开始时间不能早于发放开始时间"));
                return;
              }
            }
            // 验证使用开始时间必须早于使用结束时间
            if (value && form.endTime) {
              if (new Date(value) >= new Date(form.endTime)) {
                callback(new Error("使用开始时间必须早于使用结束时间"));
                return;
              }
            }
          }
          callback();
        },
        trigger: "change"
      }
    ],
    endTime: [
      {
        validator: (rule, value, callback) => {
          if (form.useTimeType === 2) {
            if (!value) {
              callback(new Error("请选择使用结束时间"));
              return;
            }
            // 验证使用结束时间必须晚于使用开始时间
            if (form.startTime && new Date(value) <= new Date(form.startTime)) {
              callback(new Error("使用结束时间必须晚于使用开始时间"));
              return;
            }
            // 验证使用结束时间不能早于发放结束时间
            if (
              form.distributionEndTime &&
              new Date(value) < new Date(form.distributionEndTime)
            ) {
              callback(new Error("使用结束时间不能早于发放结束时间"));
              return;
            }
          }
          callback();
        },
        trigger: "change"
      }
    ],
    conditionAmount: [
      { required: true, message: "请输入满减条件金额", trigger: "blur" }
    ],
    discountAmount: [
      { required: true, message: "请输入优惠金额", trigger: "blur" },
      {
        validator: (rule, value, callback) => {
          console.log("🦄-----value-----", value, form.conditionAmount);
          if (form.couponDiscountType === "FULL_REDUCTION") {
            if (value === null || value === undefined || value === "") {
              callback(new Error("请输入优惠金额"));
            } else if (value <= 0) {
              callback(new Error("优惠金额必须大于0"));
            } else if (value > 1000000) {
              callback(new Error("优惠金额不能超过1,000,000"));
            } else if (
              form.conditionAmount > 0 &&
              value > form.conditionAmount
            ) {
              const discountValue = parseFloat(value) || 0;
              const conditionValue = parseFloat(form.conditionAmount) || 0;

              if (conditionValue > 0 && discountValue > conditionValue) {
                callback(new Error("优惠金额不能大于满减条件金额"));
              } else {
                callback();
              }
            } else {
              callback();
            }
          } else {
            callback();
          }
        },
        trigger: "blur"
      }
    ],
    noLimitNumber: [
      {
        validator: (rule, value, callback) => {
          if (value && value.trim() !== "") {
            if (isNaN(parseInt(value)) || parseInt(value) <= 0) {
              callback(new Error("请输入有效的每人限领数量"));
            } else {
              callback();
            }
          } else {
            callback();
          }
        },
        trigger: "blur"
      }
    ]
  };

  // 监听使用时间类型变化，自动清空时间范围
  watch(
    () => form.useTimeType,
    (newValue, oldValue) => {
      if (newValue === 1) {
        // 选择"不限"时，清空时间范围
        form.useTimeRange = [];
        form.startTime = null;
        form.endTime = null;
        form.isUseLimit = false;
      } else if (newValue === 2) {
        // 选择"有限"时，设置使用时间限制
        form.isUseLimit = true;
      }
    }
  );

  // 监听费用类型变化
  watch(
    () => form.feeType,
    newValue => {
      console.log("费用类型变化:", newValue);
      // 可以根据费用类型的变化来调整其他相关字段
      // 例如：如果选择材料类型，可能需要调整某些验证规则
    }
  );

  // 监听使用时间范围变化，同步到API字段
  watch(
    () => form.startTime,
    newValue => {
      if (newValue) {
        // 修复：确保时间值正确设置
        form.startTime = newValue;
      } else {
        form.startTime = null;
      }
    }
  );

  // 监听使用结束时间变化，同步到API字段
  watch(
    () => form.endTime,
    newValue => {
      if (newValue) {
        // 修复：确保时间值正确设置
        form.endTime = newValue;
      } else {
        form.endTime = null;
      }
    }
  );

  // 监听发放开始时间变化，同步到API字段
  watch(
    () => form.distributionStartTime,
    newValue => {
      if (newValue) {
        // 修复：确保时间值正确设置
        form.distributionStartTime = newValue;
      } else {
        form.distributionStartTime = null;
      }
    }
  );

  // 监听发放结束时间变化，同步到API字段
  watch(
    () => form.distributionEndTime,
    newValue => {
      if (newValue) {
        // 修复：确保时间值正确设置
        form.distributionEndTime = newValue;
      } else {
        form.distributionEndTime = null;
      }
    }
  );
  // 表单文件数据
  const formFile = ref({
    institutionLicense: [],
    qualificationDocuments: [],
    logo: [],
    video: [],
    environment: []
  });

  // 基本信息表单配置 - 按照 create.vue 的字段调整
  const formData = ref([
    {
      label: "优惠券名称",
      type: "input",
      types: "text",
      prop: "name",
      check: true,
      width: "400px",
      placeholder: "请输入优惠券名称",
      maxLength: 10
    },
    {
      label: "优惠券费用类型",
      type: "radio",
      check: true,
      width: "400px",
      prop: "feeType",
      placeholder: "请选择费用类型",
      options: [
        { name: "课时", value: "CLASS_HOUR" },
        { name: "材料", value: "MATERIAL" }
      ]
    },
    {
      label: "优惠券类型",
      type: "radio",
      check: true,
      width: "400px",
      prop: "couponDiscountType",
      placeholder: "请选择优惠类型",
      options: [{ name: "满减券", value: "FULL_REDUCTION" }]
    },
    {
      label: "满减金额",
      type: "radioInput",
      check: true,
      prop: "conditionAmount",
      width: "400px",
      placeholder: "满减条件设置",
      // 只有当选择满减券时才显示
      show: () => form.couponDiscountType === "FULL_REDUCTION"
    },
    {
      label: "发放数量",
      type: "input",
      types: "number",
      check: true,
      maxLength: 11,
      width: "400px",
      prop: "totalIssue",
      placeholder: "请输入优惠券发放数量"
    },
    {
      label: "发放时间",
      type: "date",
      check: false,
      prop: "distributionStartTime",
      width: "400px",
      placeholder: "请选择发放时间"
    },
    {
      label: "使用时间",
      type: "radio",
      check: true,
      prop: "useTimeType",
      width: "400px",
      placeholder: "请选择使用时间限制",
      options: [
        { name: "不限", value: 1 },
        { name: "有限", value: 2 }
      ]
    },
    {
      label: " ",
      type: "date",
      check: false,
      prop: "startTime",
      width: "400px",
      placeholder: "请选择使用开始时间",
      show: () => form.useTimeType === 2
    },
    {
      label: "备注",
      type: "textarea",
      prop: "remarks",
      width: "400px",
      maxLength: 200,
      placeholder: "请输入备注信息"
    },
    {
      label: "状态",
      type: "radio",
      check: true,
      prop: "enabled",
      width: "400px",
      placeholder: "请选择状态",
      options: [
        { name: "启用", value: true },
        { name: "停用", value: false }
      ]
    }
  ]);

  // 使用范围表单配置
  const formData2 = ref([
    {
      label: "使用范围",
      type: "radio",
      check: true,
      maxLength: 50,
      width: "400px",
      prop: "usedCourse",
      placeholder: "请选择使用范围",
      options: [
        { name: "通用", value: "ALL" },
        { name: "指定", value: "LIMIT" }
      ]
    }
  ]);

  // 优惠券使用范围相关数据 - 根据API文档参数
  const scopeForm = reactive({
    courseName: "",
    coursePeriodName: "",
    startTime: "",
    endTime: "",
    coursePeriodState: ""
  });

  // 搜索列配置 - 根据API文档参数
  const searchColumns = ref([
    {
      label: "课程名称",
      prop: "courseName",
      type: "input",
      placeholder: "请输入课程名称",
      span: 6
    },
    {
      label: "课期名称",
      prop: "coursePeriodName",
      type: "input",
      placeholder: "请输入课期名称",
      span: 6
    }
  ]);
  const handleTotalIssueInput = value => {
    // 只允许输入数字
    if (value === "" || value === null || value === undefined) {
      return "";
    }

    // 移除非数字字符（除了数字）
    let filteredValue = value.toString().replace(/\D/g, "");

    // 移除前导零（除非是单独的0）
    if (filteredValue.length > 1 && filteredValue.startsWith("0")) {
      filteredValue = filteredValue.replace(/^0+/, "");
    }

    // 限制最大值为1000000
    const numValue = parseInt(filteredValue);
    if (numValue > 1000000) {
      filteredValue = "1000000";
    }

    // 更新表单值
    form.totalIssue = filteredValue;

    return filteredValue;
  };
  // 表格列配置 - 根据API返回数据结构
  const tableColumns = ref([
    {
      type: "selection",
      width: 55,
      align: "center",
      reserveSelection: true,
      selectable: row => true
    },
    {
      label: "课程名称",
      prop: "courseName",
      minWidth: 150
    },
    {
      label: "课期名称",
      prop: "coursePeriodName",
      minWidth: 200
    },
    {
      label: "期号",
      prop: "termNumber",
      width: 120,
      align: "center"
    },
    {
      label: "开课时间",
      prop: "openTime",
      width: 120,
      align: "center",
      formatter: ({ openTime }) => {
        if (!openTime || openTime === "-") return "-";
        return formatTime(openTime, "YYYY-MM-DD HH:mm");
      }
    },
    {
      label: "购买类型",
      prop: "buyType",
      width: 120,
      align: "center",
      formatter: ({ buyType }) => {
        const buyTypeMap = {
          ORDINARY: "普通单",
          PRIVATE_DOMAIN_GROUP_ORDER: "团购单"
        };
        return buyTypeMap[buyType] || buyType || "-";
      }
    },
    {
      label: "课程状态",
      prop: "coursePeriodState",
      width: 100,
      align: "center",
      formatter: ({ coursePeriodState }) => {
        const stateMap = {
          OFFLINE: "下架",
          OFFLINE_UNDER_REVIEW: "下架审核中",
          ONLINE: "上架",
          ONLINE_UNDER_REVIEW: "上架审核中",
          COMPLETED: "已完成",
          NOT_LISTED: "未上架"
        };

        const stateText =
          stateMap[coursePeriodState] || coursePeriodState || "-";
        return stateText;
      }
    }
  ]);

  // 初始数据 - 课程列表
  const tableData = ref([]);

  // 确保表格数据的稳定性
  const stableTableData = computed(() => {
    return tableData.value.map(item => {
      return {
        courseName: item.course.name,
        coursePeriodName: item.name,
        termNumber: item.termNumber,
        openTime: item.openTime,
        buyType: item.buyType,
        coursePeriodState: item.coursePeriodState
      };
    });
  });

  // 分页配置 - 根据API文档，page从0开始
  const pagination = reactive({
    page: 0,
    size: 10,
    total: 0
  });

  // 加载状态
  const loading = ref(false);
  const getListLoading = ref(false);

  // 选中的行
  const selectedRows = ref([]);

  // 已选中的行 ID（初始回显数据）
  const selectedRowKeys = ref([]);

  // 存储选中行的详细信息
  const rowList = ref({
    coursePeriodId: null,
    couponAvailable: null,
    couponIds: []
  });

  // 计算属性：是否显示使用范围选择区域
  const showScopeSelection = computed(() => {
    return form.usedCourse === "LIMIT"; // 当选择"指定"时显示
  });

  // 搜索方法
  const onSearch = async () => {
    if (loading.value) return; // 防止重复搜索

    // 通用模式下不需要搜索课程
    if (form.usedCourse === "ALL") {
      loading.value = false;
      return;
    }

    loading.value = true;
    try {
      // 构建查询参数
      const params = {
        page: pagination.page,
        size: Number(pagination.size),
        sort: "createdAt,desc",
        courseName: scopeForm.courseName || undefined,
        coursePeriodName: scopeForm.coursePeriodName || undefined
      };

      // 移除undefined的参数
      Object.keys(params).forEach(key => {
        if (params[key] === undefined) {
          delete params[key];
        }
      });

      // 调用API
      const result = await getCouponCourse(params);

      if (result.code === 200) {
        pagination.total = result.data.totalElements || 0;
        pagination.page = result.data.number || 0;
        pagination.size = result.data.size || 20;

        // 设置表格数据
        tableData.value =
          result.data?.content?.map(item => {
            return {
              id: item.id,
              courseName: item.course?.name || "-",
              coursePeriodName: item?.name || "-",
              termNumber: item?.termNumber || "-",
              openTime: item?.openTime || "-",
              buyType: item?.buyType || "-",
              coursePeriodState: item?.coursePeriodState || "-"
            };
          }) || [];
      } else {
        ElMessage.error(result.msg || "查询失败");
      }
    } catch (error) {
      ElMessage.error("查询课程失败");
    } finally {
      loading.value = false;
    }
  };

  // 重置方法
  const onReset = () => {
    Object.keys(scopeForm).forEach(key => {
      scopeForm[key] = "";
    });
    pagination.page = 0;
    onSearch();
  };

  // 分页改变 - 根据API文档，page从0开始
  const onPageChange = page => {
    pagination.page = page - 1; // 转换为API的page格式（从0开始）
    onSearch();
  };

  // 每页条数改变
  const onSizeChange = size => {
    pagination.size = size;
    pagination.page = 0; // 重置到第一页（API格式）
    onSearch();
  };

  // 表格引用
  const tableRef = ref(null);

  // 选择改变
  const onSelectionChange = (selection, row, column) => {
    // 防止递归更新
    if (
      JSON.stringify(selectedRows.value.map(row => row.id).sort()) ===
      JSON.stringify(selection.map(row => row.id).sort())
    ) {
      return;
    }

    // 更新选中的行（完整行数据）
    selectedRows.value = [...selection];

    // 同步更新selectedRowKeys（仅ID数组，用于复选框回显）
    selectedRowKeys.value = selectedRows.value;
    // 自动勾选匹配的行
    if (selectedRows.value.length > 0) {
      selectedRows.value.forEach(row => {
        tableRef.value?.getTableRef()?.toggleRowSelection(row, true);
      });
    }

    // 更新rowList（使用完整行数据）
    if (selection && selection.length > 0) {
      rowList.value = {
        coursePeriodId: selection[0].id,
        couponAvailable: true,
        couponIds: selection.map(row => row.id)
      };
    } else {
      rowList.value = {
        coursePeriodId: null,
        couponAvailable: null,
        couponIds: []
      };
    }
  };

  // 监听使用范围变化
  const onScopeChange = value => {
    // 使用 nextTick 避免在同一个事件循环中更新
    nextTick(() => {
      if (value === "ALL") {
        // 选择"通用"时，清空选中的课程
        selectedRows.value = [];
        selectedRowKeys.value = [];
        form.couponScope = "ALL";
        form.coursePeriodIds = [];
      } else if (value === "LIMIT") {
        // 选择"指定"时，设置使用范围并加载课程列表
        form.couponScope = "LIMIT";
        // 指定模式下需要加载课程列表供选择
        onSearch();
      }
    });
  };

  // 返回上一页
  const reset = () => {
    ElMessageBox.confirm(
      "确定要取消新建优惠券吗？未保存的数据将丢失。",
      "确认取消",
      {
        confirmButtonText: "确定",
        cancelButtonText: "继续编辑"
      }
    )
      .then(() => {
        router.go(-1);
      })
      .catch(() => {
        // 用户选择继续编辑
      });
    // router.go(-1);
  };

  // 处理时间字段转换
  const handleTimeField = timeValue => {
    if (!timeValue) return 0;
    if (Array.isArray(timeValue) && timeValue.length === 2) {
      // 如果是日期范围数组，取开始时间
      return new Date(timeValue[0]).getTime();
    }
    if (typeof timeValue === "string") {
      return new Date(timeValue).getTime();
    }
    return timeValue;
  };

  // 处理发放时间
  const handleDistributionTime = timeValue => {
    if (!timeValue) return { start: 0, end: 0 };
    if (Array.isArray(timeValue) && timeValue.length === 2) {
      return {
        start: new Date(timeValue[0]).getTime(),
        end: new Date(timeValue[1]).getTime()
      };
    }
    return { start: 0, end: 0 };
  };

  // 处理满减条件金额变化
  const handleConditionAmountChange = () => {
    // 触发优惠金额的验证
    if (formRef.value) {
      formRef.value.validateField("discountAmount");
    }
  };

  // 处理优惠金额变化
  const handleDiscountAmountChange = () => {
    // 触发满减条件金额的验证
    if (formRef.value) {
      formRef.value.validateField("conditionAmount");
    }
  };

  // 处理使用开始时间变化
  const handleStartTimeChange = () => {
    // 触发使用开始时间和使用结束时间的验证
    if (formRef.value) {
      formRef.value.validateField("startTime");
      formRef.value.validateField("endTime");
    }
  };

  // 处理使用结束时间变化
  const handleEndTimeChange = () => {
    // 触发使用结束时间和使用开始时间的验证
    if (formRef.value) {
      formRef.value.validateField("endTime");
      formRef.value.validateField("startTime");
    }
  };

  // 处理发放结束时间变化
  const handleDistributionEndTimeChange = () => {
    // 触发发放结束时间的验证
    if (formRef.value) {
      formRef.value.validateField("distributionEndTime");
    }
  };

  // 提交处理
  const onSubmit = async () => {
    if (getListLoading.value) return;

    try {
      // 表单验证
      const valid = await formRef.value.validate();

      if (!valid) {
        return;
      }

      // 验证使用范围
      if (form.usedCourse === "LIMIT" && selectedRows.value.length === 0) {
        ElMessage.warning("请至少选择一个课程");
        return;
      }

      // 构建API请求数据 - 按照 create.vue 的格式
      let paramsData = {
        // 必填字段
        name: form.name || "", // string - 名称
        feeType: form.feeType || "CLASS_HOUR", // string - 费用类型
        couponDiscountType: form.couponDiscountType || "", // string - 优惠类型
        totalIssue: parseInt(form.totalIssue) || 0, // integer(int32) - 发放数量
        distributionStartTime: 0, // integer(int64) - 发放开始时间，将在下面处理
        distributionEndTime: 0, // integer(int64) - 结束发放时间，将在下面处理
        isUseLimit: Boolean(form.isUseLimit), // boolean - 使用是否有限制
        couponScope: form.couponScope || "ALL", // string - 优惠券使用范围
        enabled: Boolean(form.enabled), // boolean - 启用状态

        // 可选字段
        conditionAmount: form.conditionAmount
          ? parseFloat(form.conditionAmount)
          : 0, // number - 满减条件金额
        discountAmount: form.discountAmount
          ? parseFloat(form.discountAmount)
          : 0, // number - 优惠值
        startTime: form.startTime || "", // integer(int64) - 使用开始时间
        endTime: form.endTime || "", // integer(int64) - 使用结束时间
        remarks: form?.remarks || "", // string - 备注
        coursePeriodIds: Array.isArray(form.coursePeriodIds)
          ? form.coursePeriodIds
          : [] // array - 可使用课期
      };

      // 处理发放时间
      if (form.distributionStartTime && form.distributionEndTime) {
        // 修复：正确处理时间转换
        paramsData.distributionStartTime = new Date(
          form.distributionStartTime
        ).getTime();
        paramsData.distributionEndTime = new Date(
          form.distributionEndTime
        ).getTime();
      } else {
        // 如果没有设置发放时间，使用当前时间作为默认值
        paramsData.distributionStartTime = Date.now();
        paramsData.distributionEndTime = Date.now();
      }

      // 处理使用时间
      if (form.useTimeType === 2 && form.startTime && form.endTime) {
        // 使用时间有限制
        paramsData.isUseLimit = true;
        paramsData.startTime = new Date(form.startTime).getTime();
        paramsData.endTime = new Date(form.endTime).getTime();
      } else {
        // 使用时间不限制
        paramsData.isUseLimit = false;
        paramsData.startTime = 0;
        paramsData.endTime = 0;
      }

      // 如果选择"指定"使用范围，设置课期ID数组（从完整行数据中提取ID）
      if (form.usedCourse === "LIMIT") {
        paramsData.coursePeriodIds = selectedRows.value.map(row => row.id);
      }

      // 处理满减券金额字段
      if (form.couponDiscountType === "FULL_REDUCTION") {
        // 满减券：满金额 -> conditionAmount，减金额 -> discountAmount
        if (form.conditionAmount && parseFloat(form.conditionAmount) > 0) {
          paramsData.conditionAmount = parseFloat(form.conditionAmount);
        }
        if (form.discountAmount && parseFloat(form.discountAmount) > 0) {
          paramsData.discountAmount = parseFloat(form.discountAmount);
        }
      }

      // 过滤掉值为 0、空字符串、空数组、null、undefined 的字段
      const filteredParamsData = {};
      Object.keys(paramsData).forEach(key => {
        const value = paramsData[key];
        // 保留必填字段，过滤掉可选字段中的无效值
        if (
          key === "name" ||
          key === "feeType" ||
          key === "couponDiscountType" ||
          key === "totalIssue" ||
          key === "distributionStartTime" ||
          key === "distributionEndTime" ||
          key === "isUseLimit" ||
          key === "couponScope" ||
          key === "enabled"
        ) {
          // 必填字段，保留所有值（包括0）
          filteredParamsData[key] = value;
        } else {
          // 可选字段，过滤掉无效值
          if (
            value !== 0 &&
            value !== "" &&
            value !== null &&
            value !== undefined
          ) {
            if (Array.isArray(value)) {
              // 数组类型，过滤掉空数组
              if (value.length > 0) {
                filteredParamsData[key] = value;
              }
            } else {
              filteredParamsData[key] = value;
            }
          }
        }
      });

      console.log("提交的API数据:", filteredParamsData);

      // 调用新增优惠券API
      getListLoading.value = true;
      const result = await addCoupon(filteredParamsData);

      if (result.code === 200) {
        ElMessage.success("优惠券创建成功！");
        router.go(-1);
      } else {
        ElMessage.error(result.message || "创建失败，请重试");
      }
    } catch (error) {
      console.error("创建优惠券失败:", error);
      ElMessage.error("创建失败，请重试");
    } finally {
      getListLoading.value = false;
    }
  };

  // 提交表单
  const submitForm = debounce(
    () => {
      formRef.value.validate(valid => {
        if (valid) {
          onSubmit();
        } else {
        }
      });
    },
    1000,
    { immediate: true }
  );

  // 其他数据
  const newData = ref();
  const oldData = ref();

  // 加载课期信息并勾选已有项
  const loadCoursePeriodsAndSelectExisting = async coursePeriods => {
    try {
      // 获取所有可用的课期数据
      const params = {
        page: 0,
        size: 1000, // 获取足够多的数据
        sort: "createdAt,desc"
      };

      const result = await getCouponCourse(params);
      coursePeriods = coursePeriods.map(item => item.id);

      if (result.code === 200 && result.data?.content) {
        const rowsToSelect = result.data.content.filter(row =>
          coursePeriods.includes(row.id)
        );

        if (rowsToSelect.length > 0) {
          // 设置选中的行
          selectedRows.value = rowsToSelect;

          // 更新rowList
          rowList.value = {
            coursePeriodIds: rowsToSelect.map(row => row.id),
            couponAvailable: true,
            couponIds: Number(coursePeriods)
          };

          // 使用 nextTick 确保表格已渲染完成
          await nextTick();

          // 自动勾选匹配的行
          // rowsToSelect.forEach(row => {
          //   tableRef.value?.getTableRef()?.toggleRowSelection(row, true);
          // });
          if (tableRef.value?.getTableRef) {
            const elTable = tableRef.value.getTableRef();
            if (elTable && elTable.toggleRowSelection) {
              rowsToSelect.forEach(row => {
                elTable.toggleRowSelection(row, true);
              });
            }
          }
        }
      }
    } catch (error) {
      console.error("🍪-----加载课期数据失败:", error);
    }
  };

  // 加载优惠券数据
  const loadCouponData = async id => {
    try {
      loading.value = true;
      const result = await getCouponFindById({ id });

      if (result.code === 200 && result.data) {
        const couponData = result.data;

        try {
          // 回显表单数据
          form.name = couponData.name || "";
          form.feeType = couponData.feeType || "CLASS_HOUR";
          form.couponDiscountType =
            couponData.couponDiscountType || "FULL_REDUCTION";
          form.totalIssue = couponData.totalIssue
            ? String(couponData.totalIssue)
            : "";
          form.remarks = couponData.remarks || "";
          form.enabled =
            couponData.enabled !== undefined ? couponData.enabled : true;
          form.couponScope = couponData.couponScope || "ALL";
          form.usedCourse = couponData.couponScope || "ALL"; // 同步使用范围
          // formatTime(openTime, "YYYY-MM-DD HH:mm")
          form.distributionStartTime =
            formatTime(couponData.distributionStartTime, "YYYY-MM-DD") || "";
          form.distributionEndTime =
            formatTime(couponData.distributionEndTime, "YYYY-MM-DD") || "";
          form.startTime = formatTime(couponData.startTime, "YYYY-MM-DD") || "";
          form.endTime = formatTime(couponData.endTime, "YYYY-MM-DD") || "";

          // 处理满减金额
          if (couponData.couponDiscountType === "FULL_REDUCTION") {
            form.conditionAmount = couponData.conditionAmount
              ? String(couponData.conditionAmount)
              : "";
            form.discountAmount = couponData.discountAmount
              ? String(couponData.discountAmount)
              : "";
          }

          if (form.startTime && form.endTime) {
            form.useTimeType = 2;
            form.isUseLimit = true;
          }

          // 处理使用范围
          if (
            couponData.couponScope === "LIMIT" &&
            couponData.coursePeriods &&
            couponData.coursePeriods.length > 0
          ) {
            form.usedCourse = "LIMIT";
            form.couponScope = "LIMIT";
            // 从coursePeriods中提取ID
            form.coursePeriodIds = couponData.coursePeriods.map(
              item => item.id
            );

            // 如果是指定范围，需要加载课程列表并勾选已有项
            await loadCoursePeriodsAndSelectExisting(couponData.coursePeriods);
          }
        } catch (fieldError) {
          ElMessage.warning("部分字段回显失败，请检查数据格式");
        }
      } else {
        ElMessage.error("获取优惠券数据失败");
      }
    } catch (error) {
      ElMessage.error("加载优惠券数据失败");
    } finally {
      loading.value = false;
    }
  };

  // 页面初始化时加载数据
  const initializePage = async () => {
    // 检查是否是编辑模式（复制）
    if (route.query.type === "edit" && route.query.id) {
      await loadCouponData(route.query.id);
      await onSearch();
    } else if (route.query.type === "copy" && route.query.id) {
      // 复制模式
      await loadCouponData(route.query.id);
      // 清空ID，避免提交时误认为是编辑
      form.id = undefined;
      await onSearch();
    } else {
      // 新建模式，不需要加载课程列表，因为所有课程都可以使用
      console.log("新建优惠券模式");
    }
  };

  // 复选框配置
  const rowSelection = {
    type: "checkbox", // 复选框类型
    selectedRowKeys: selectedRowKeys, // 绑定选中的行 ID，不需要.value
    onChange: newSelectedRowKeys => {
      // 选中状态变化时更新 selectedRowKeys
      selectedRowKeys.value = newSelectedRowKeys;

      // 根据选中的ID找到对应的完整行数据
      const selectedRowsData = tableData.value.filter(item =>
        newSelectedRowKeys.includes(item.id)
      );

      // 更新selectedRows（完整行数据）
      selectedRows.value = selectedRowsData;

      // 更新rowList（使用完整行数据）
      if (selectedRowsData.length > 0) {
        rowList.value = {
          coursePeriodId: selectedRowsData[0].id,
          couponAvailable: true,
          couponIds: newSelectedRowKeys
        };
      } else {
        rowList.value = {
          coursePeriodId: null,
          couponAvailable: null,
          couponIds: []
        };
      }
    }
  };

  // 页面加载完成后初始化
  onMounted(() => {
    try {
      initializePage();
    } catch (err) {
      console.error("🍪-----页面初始化失败:", err);
    }
  });

  return {
    // 路由相关
    router,
    route,

    // 表单相关
    form,
    formRef,
    rules,
    formData,
    formData2,
    // 使用范围相关
    scopeForm,
    searchColumns,
    tableColumns,
    tableData,
    stableTableData,
    showScopeSelection,

    // 分页相关
    pagination,

    // 状态相关
    loading,
    getListLoading,
    selectedRows,
    selectedRowKeys,
    richFlag,
    formFile,

    // 表格引用
    tableRef,
    rowSelection,

    // 方法
    onSearch,
    onReset,
    onPageChange,
    onSizeChange,
    onSelectionChange,
    onScopeChange,
    reset,
    submitForm,
    onSubmit,
    handleConditionAmountChange,
    handleDiscountAmountChange,
    handleStartTimeChange,
    handleEndTimeChange,
    handleDistributionEndTimeChange,
    handleTotalIssueInput,
    // 其他数据
    newData,
    oldData
  };
}
