# 草稿箱优惠券数据流转功能实现说明

## 功能概述

本次实现了草稿箱功能中优惠券数据从价格设置页面到完成页面的完整流转逻辑，解决了完成页面优惠券表格为空的问题。

## 实现的功能

### 1. 数据收集（价格设置页面）
- **跨分页数据收集**：在价格设置页面的"可用指定优惠券"表格中，当用户勾选优惠券时，系统会记录所有被选中的优惠券完整数据，支持跨分页操作
- **数据去重处理**：对选中的优惠券数据进行去重处理，确保同一优惠券不会重复记录
- **实时数据更新**：用户每次选择/取消选择优惠券时，系统会实时更新选中的优惠券数据

### 2. 数据传递（组件通信）
- **父子组件通信**：价格设置组件通过`couponDataChange`事件将优惠券数据传递给父组件（courseCreate.vue）
- **数据结构扩展**：扩展了优惠券数据结构，新增`selectedCouponsData`字段存储完整的优惠券信息
- **状态同步**：确保优惠券可用状态变化时，相关数据能够正确同步

### 3. 数据展示（完成页面）
- **外部数据支持**：完成页面的价格设置组件支持接收外部传入的优惠券数据
- **条件显示**：优惠券表格只在有选中数据时才显示，避免空表格的展示
- **无接口调用**：完成页面不调用接口获取优惠券数据，直接使用从价格设置页面传递的数据

## 技术实现细节

### 修改的文件

1. **src/views/course/components/priceEdite.vue**
   - 新增`allSelectedCouponsData`变量存储完整优惠券数据
   - 修改`handleCouponSelectionChange`函数，收集完整优惠券数据并去重
   - 修改`emitCouponData`函数，传递完整优惠券数据
   - 优化数据恢复逻辑，确保跨分页数据完整性

2. **src/views/course/courseCreate.vue**
   - 扩展`couponData`数据结构，新增`selectedCouponsData`字段

3. **src/views/course/components/complete.vue**
   - 修改props定义，支持完整优惠券数据
   - 将优惠券数据传递给PriceSetting组件

4. **src/views/course/components/priceSetting.vue**
   - 新增props支持外部优惠券数据
   - 修改`getCouponList`函数，支持使用外部数据
   - 添加watch监听外部数据变化
   - 添加条件显示逻辑

### 核心数据流

```
价格设置页面选择优惠券 
    ↓
收集完整优惠券数据（跨分页 + 去重）
    ↓
通过事件传递给父组件
    ↓
父组件存储优惠券数据
    ↓
传递给完成页面的价格设置组件
    ↓
完成页面展示选中的优惠券
```

## 使用说明

### 用户操作流程
1. 在草稿箱的价格设置页面，将"可以使用优惠券"设置为"是"
2. 在优惠券表格中勾选需要的优惠券（支持跨分页选择）
3. 点击"下一步"进入完成页面
4. 在完成页面的价格设置部分可以看到之前选中的优惠券
5. 点击"完成新建"时，选中的优惠券ID会通过接口提交

### 技术特点
- **数据完整性**：不仅保存优惠券ID，还保存完整的优惠券信息用于展示
- **跨分页支持**：用户可以在不同页面选择优惠券，系统会正确记录所有选择
- **去重机制**：自动处理重复选择的优惠券
- **状态同步**：优惠券可用状态变化时，相关数据会正确清空或恢复

## 注意事项

1. **数据持久化**：草稿箱只保存优惠券是否可用的状态（`couponAvailable`），不保存具体的优惠券选择状态。优惠券选择状态只在当前编辑会话中保持
2. **页面刷新**：如果用户刷新页面或重新进入草稿箱，之前的优惠券选择状态会丢失，需要重新选择
3. **只读展示**：完成页面的优惠券表格是只读的，用户无法在此页面修改选择
4. **修改选择**：如果需要修改优惠券选择，用户需要返回价格设置页面
5. **数据提交**：优惠券数据的提交仍然通过原有的`/organization/coursePeriod/saveV2`接口
6. **去重处理**：系统会自动处理优惠券ID的去重，确保提交数据的正确性
7. **分页支持**：完成页面的优惠券表格支持分页显示，但不会调用接口获取数据

## 功能测试清单

### 基础功能测试
1. **优惠券可用状态切换**
   - [ ] 在价格设置页面将"可以使用优惠券"从"否"切换到"是"，验证优惠券表格是否正确显示
   - [ ] 将状态从"是"切换到"否"，验证选中的优惠券数据是否被清空

2. **优惠券选择功能**
   - [ ] 在优惠券表格中选择单个优惠券，验证选择状态是否正确
   - [ ] 选择多个优惠券，验证多选功能是否正常
   - [ ] 取消选择优惠券，验证取消选择功能是否正常

3. **跨分页选择测试**
   - [ ] 在第一页选择优惠券，切换到第二页，验证第一页的选择状态是否保持
   - [ ] 在第二页选择优惠券，返回第一页，验证两页的选择状态是否都保持
   - [ ] 在不同页面取消选择，验证跨页面的取消选择功能

### 数据传递测试
4. **页面间数据传递**
   - [ ] 在价格设置页面选择优惠券后，进入完成页面，验证优惠券数据是否正确传递
   - [ ] 验证完成页面显示的优惠券信息是否与价格设置页面选择的一致
   - [ ] 验证优惠券表格的分页功能在完成页面是否正常工作

5. **数据去重测试**
   - [ ] 在不同页面选择相同的优惠券，验证系统是否正确去重
   - [ ] 验证最终传递到完成页面的数据中没有重复的优惠券

### 边界情况测试
6. **状态变化测试**
   - [ ] 选择优惠券后，将"可以使用优惠券"设置为"否"，再设置为"是"，验证选择状态是否被正确清空
   - [ ] 在草稿箱模式下，验证优惠券可用状态的保存和恢复

7. **完成页面测试**
   - [ ] 验证完成页面的优惠券表格是只读的（无选择功能）
   - [ ] 验证当没有选中优惠券时，完成页面不显示优惠券表格
   - [ ] 验证完成页面的优惠券表格分页功能不会调用接口

8. **最终提交测试**
   - [ ] 点击"完成新建"按钮，验证选中的优惠券ID是否正确提交到`/organization/coursePeriod/saveV2`接口
   - [ ] 验证提交的优惠券ID数组是否已去重
   - [ ] 验证提交成功后的页面跳转是否正常

### 异常情况测试
9. **异常处理测试**
   - [ ] 在网络异常情况下，验证优惠券列表获取失败时的处理
   - [ ] 验证优惠券数据为空时的界面显示
   - [ ] 验证页面刷新后的数据恢复情况
